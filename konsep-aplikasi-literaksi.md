# Konsep Aplikasi LiterAksi Board Game Support

## Overview

Aplikasi ini dirancang untuk mensupport permainan board game LiterAksi dengan menggantikan peran instruktur. Aplikasi akan menyediakan bank soal, timer, sistem scoring, dan mengelola berbagai tantangan yang ada dalam permainan.

## Tujuan Aplikasi

- **Bukan pengganti** permainan board fisik
- **Mensupport** permainan dengan menggantikan instruktur
- Menyediakan konten tantangan secara otomatis dan acak
- Mengelola waktu dan sistem poin
- Memfasilitasi permainan yang lebih lancar dan fair

## Fitur Utama

### 1. <PERSON><PERSON><PERSON><PERSON> Pemain/Tim
- Regis<PERSON>i pemain atau tim (2+ pemain)
- Tracking skor masing-masing pemain/tim
- Penentuan giliran bermain
- Leaderboard real-time

### 2. Bank Soal untuk Setiap Jenis Kartu

#### Kartu Hoax atau Fakta
- Database informasi dengan label hoax/fakta
- Sistem penyampaian informasi secara acak
- Timer 2 menit
- Sistem poin: +2 jika benar, +2 untuk pemberi informasi jika salah

#### Kartu Huruf Acak
- Generator huruf acak untuk membentuk kata
- Database kata-kata target
- Timer 3 menit
- Sistem poin: +2 jika benar

#### Kartu Tebak Kata
- Mode Individu: Database sinonim/antonim
- Mode Kelompok: Bank kata untuk ditempel di kepala + sistem clue
- Timer 4 menit
- Sistem poin: +4 jika benar

#### Kartu Merangkai Cerita
- Database gambar/prompt untuk cerita
- Mode individu dan kelompok
- Timer 6 menit
- Sistem poin bertingkat: Sempurna (6), Bagus (4), Lumayan (2)

#### Kartu Kolaborasi Berdampak
- Database tantangan kolaboratif
- Timer 8 menit
- Sistem poin: Kompak (10), Tidak main (0)

### 3. Sistem Challenge Khusus
- Kuis puisi
- Tantangan kreatif lainnya
- Sistem reward/punishment

### 4. Timer Management
- Timer otomatis untuk setiap jenis tantangan
- Notifikasi waktu habis
- Pause/resume functionality

### 5. Petak Khusus Handler
- **Rest Area**: Random motivational quotes dari buku
- **Mundur Wir!!**: Simulasi dadu untuk mundur
- **Challenge**: Random challenge assignment
- **Poin (+/-)**: Otomatis menghitung perubahan poin

## Arsitektur Aplikasi

### Frontend (Client-side Only)
- **Framework**: React.js atau Vue.js
- **UI Components**:
  - Dashboard permainan
  - Tampilan tantangan untuk setiap jenis kartu
  - Timer display
  - Scoreboard
  - Player management interface
- **Responsive Design**: Dapat diakses dari tablet/smartphone
- **Data Storage**: Local Storage browser untuk game state dan progress
- **Question Bank**: Static JSON files yang di-load saat aplikasi dimulai
- **State Management**: Redux/Vuex untuk mengelola state permainan

### Data Structure (JSON Files)

```json
// data/hoax_facts.json
[
  {
    "id": 1,
    "statement": "...",
    "is_fact": true,
    "category": "teknologi"
  }
]

// data/random_letters.json
[
  {
    "id": 1,
    "letters": ["A", "N", "G", "I", "N"],
    "target_words": ["ANGIN", "AGING", "NAGIN"]
  }
]

// data/word_guess.json
[
  {
    "id": 1,
    "word": "bahagia",
    "synonyms": ["gembira", "senang"],
    "antonyms": ["sedih", "murung"],
    "difficulty": "easy"
  }
]

// data/story_prompts.json
[
  {
    "id": 1,
    "image_url": "assets/images/story1.jpg",
    "description": "Seorang anak di hutan",
    "category": "petualangan"
  }
]

// data/collaboration_tasks.json
[
  {
    "id": 1,
    "task_description": "Buat gerakan untuk menjelaskan 'kerja sama'",
    "requirements": "Semua anggota tim terlibat",
    "duration": 480
  }
]

// data/motivational_quotes.json
[
  {
    "id": 1,
    "quote": "Membaca adalah jendela dunia",
    "author": "Anonymous",
    "book_source": "Kumpulan Kata Bijak"
  }
]

// data/challenges.json
[
  {
    "id": 1,
    "type": "creative",
    "content": "Buat puisi 4 baris tentang persahabatan",
    "points": 8,
    "duration": 300
  }
]

// Local Storage Structure
{
  "gameState": {
    "players": [
      {
        "id": 1,
        "name": "Tim A",
        "team": "merah",
        "current_score": 24,
        "position": 15
      }
    ],
    "currentPlayer": 0,
    "gameStarted": true,
    "gameId": "game_123456"
  },
  "gameHistory": [...],
  "settings": {
    "timerEnabled": true,
    "soundEnabled": true,
    "difficulty": "medium"
  }
}
```

## User Interface Flow

### 1. Setup Game
- Masukkan nama pemain/tim
- Pilih mode permainan (individu/kelompok)
- Mulai permainan

### 2. During Game
- Tampilkan giliran pemain saat ini
- Saat pemain mengambil kartu: tampilkan tantangan sesuai jenis kartu
- Timer countdown
- Input jawaban/hasil
- Update skor otomatis

### 3. Special Tiles
- Rest Area: Tampilkan quote motivasi acak
- Challenge: Tampilkan tantangan khusus
- Mundur Wir!!: Simulasi dadu mundur

### 4. Scoring & Results
- Real-time scoreboard
- Akhir permainan: tampilkan pemenang
- History permainan

## Technical Requirements

### Minimum System Requirements
- Web browser modern dengan Local Storage support (Chrome, Firefox, Safari, Edge)
- No internet connection required (setelah initial load)
- Responsive untuk tablet/smartphone
- Minimal 50MB storage untuk JSON data dan assets

### Performance Considerations
- Preload semua JSON data saat aplikasi dimulai
- Cache data di browser memory untuk akses cepat
- Fully offline setelah initial load
- Local state management untuk real-time updates
- Kompresi JSON files untuk loading yang lebih cepat

## Development Phases

### Phase 1: Core Features
- Basic player management
- Implementasi 5 jenis kartu utama
- Timer functionality
- Basic scoring system

### Phase 2: Enhanced Features
- Challenge system
- Rest area quotes
- Special tiles handler
- UI/UX improvements

### Phase 3: Advanced Features
- Offline mode
- Custom bank soal
- Statistics dan analytics
- Export/import game data

## Deployment Strategy

### Option 1: Static Web Application
- Deploy di static hosting (Vercel, Netlify, GitHub Pages)
- Progressive Web App (PWA) untuk akses mobile dan offline
- All data bundled dengan aplikasi (JSON files)
- CDN untuk fast loading worldwide

### Option 2: Local/Portable Application
- Desktop app menggunakan Electron dengan bundled data
- Portable HTML file dengan semua assets
- USB/flash drive distribution untuk akses tanpa internet
- Zip package untuk easy sharing

## Content Management

### Bank Soal (JSON Files)
- File editor interface untuk mengedit JSON langsung
- Kategorisasi berdasarkan file terpisah per tingkat kesulitan
- Import/export JSON files untuk backup dan sharing
- Validation schema untuk memastikan format data benar
- Hot reload untuk testing perubahan content

### Customization
- Custom JSON files yang dapat di-upload
- Settings panel untuk mengatur waktu dan poin per tantangan
- Template JSON yang dapat di-download dan dimodifikasi
- Local storage untuk menyimpan custom settings

## Security & Privacy

- No personal data collection atau server transmission
- All data stored locally di browser
- No external API calls setelah initial load
- Client-side data validation untuk user inputs
- Secure local storage dengan encryption option untuk sensitive data

## Future Enhancements

- WebRTC untuk multiplayer real-time tanpa server
- Voice recognition API untuk tantangan verbal
- WebAR integration untuk board game experience
- Client-side AI untuk content generation (using TensorFlow.js)
- Share results via URL/QR code
- Tournament mode dengan bracket system
- PWA offline sync untuk multiple devices
- Backup/restore game data via cloud storage APIs

---

*Aplikasi ini dirancang untuk meningkatkan pengalaman bermain LiterAksi Board Game dengan menyediakan dukungan teknologi yang seamless tanpa menggantikan esensi permainan board fisik.* 