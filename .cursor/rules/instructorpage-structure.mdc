# LiterAksi InstructorPage.vue Structure and Features

This file is the main instructor interface for the LiterAksi game. It is a large Vue SFC (Single File Component) using the Composition API, TypeScript, and Ionic Vue components. The file is organized into the following main sections:

## Main UI Sections
- **Game Setup Section**: For entering the Gemini API key, adding/removing players, and starting the game.
- **Game In Progress Section**: Shows current player info, dice rolling, card challenges (with voice input and timer), QR scanner, and leaderboard.
- **Game Finished Section**: Displays the winner and final leaderboard, with a button to start a new game.
- **Modals**: Includes QR scanner and settings modals.

## State Management
- Uses a `gameState` ref (of type `GameState`) for all game logic, loaded and mutated via `gameStateService`.
- Other key refs: `apiKey`, `geminiInitialized`, `playerNames`, `isProcessing`, `currentCard`, `currentInstruction`, `isListening`, `voiceTranscript`, `isQRScannerOpen`, `isSettingsOpen`, `gameTimer`.
- Computed properties for current player, leaderboard, QR scanner visibility, answer submission, and turn progression.

## Major Functions
- **Game Flow**: `startGame`, `rollDice`, `movePlayerAndCheckSquare`, `nextTurn`, `resetGame`.
- **Board Logic**: `getBoardSquareType`, `handleSquareType`, and handlers for each square type (rest area, challenge, finish, etc).
- **Card/Challenge Logic**: QR scanning, card presentation, answer evaluation, timer, and skipping challenges.
- **Voice Input**: Start/stop listening, process answer, and error handling.
- **Settings/Modals**: Show/hide settings and QR scanner, handle scan errors.

## Conventions
- Uses Ionic Vue UI components and icons throughout.
- All game logic is handled via services (`gameStateService`, `geminiService`, `voiceInputService`).
- Follows Composition API best practices for state and methods.

Refer to this rule for navigation, understanding, or editing of the InstructorPage.vue file.
description:
globs:
alwaysApply: false
---
