# LiterAksi Vue File Conventions

All Vue files in this project should:

- Use the Vue 3 Composition API with `<script setup lang="ts">` for TypeScript support.
- Use Ionic Vue UI components and icons for all user interface elements.
- Organize logic using refs, computed, and service modules for state and business logic.
- Prefer modular, composable functions and avoid Options API.
- Follow TypeScript best practices for type safety.

When creating or editing Vue files, always follow these conventions for consistency and maintainability.
description:
globs:
alwaysApply: false
---
