# Permainan Papan LiterAksi

## Aturan Permainan

Ju<PERSON>: 2 pemain atau lebih, bisa dimainkan dalam tim.

Persiapan:
- <PERSON><PERSON><PERSON> pemain menyiapkan alat tulis.
- <PERSON><PERSON><PERSON> pemain atau tim memilih bidak.
- Kartu-kartu ditempatkan sesuai dengan kategori: <PERSON><PERSON> atau F<PERSON>ta, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Merangkai Cerita, Kolaborasi Berdampak.
- Permainan dimulai dengan menentukan siapa yang akan bermain lebih dulu menggunakan dadu.

Langkah Permainan:
- Setiap pemain atau tim melempar dadu dan maju sesuai angka yang diperoleh.
- Jika pemain berada di petak yang sesuai gambar, mereka harus mengambil kartu dan menyelesaikan tugas yang tertera.
- Jika pemain berada di Rest Area, pemain tidak perlu mengambil kartu namun harus menyebutkan kata motivasi, bisa kutipan dari buku.
- Jika pemain berada di Mundur Wir!! maka pemain akan mundur dengan menggunakan perhitungan dadu.
- Jika pemain berada di Petak Challenge, maka instruktur atau juri dapat juga ditempatkan ke pemain lain, memberikan tantangan berupa kuis puisi atau sebagainya. Akan ada Reward atau Punishment yang akan diterima oleh pemain.
- Jika pemain berada di petak 1+, 2+, atau 2-, maka pemain akan mendapatkan poin tambahan atau pengurangan sesuai angka tersebut.

Kondisi Menang: Pemain atau tim pertama yang mencapai akhir jalur maka akan mendapatkan 10 poin dan otomatis menghentikan jalannya permainan. Setiap tantangan yang diselesaikan memberikan poin, dan pemain dengan poin tertinggi dianggap sebagai pemenang.


## Kartu-kartu Literaksi Board Game

### Kartu Hoax atau Fakta
Instruksi dari kartu ini adalah untuk mengungkapkan Hoax atau Fakta dari sebuah Informasi.

Informasi akan disampaikan oleh instruktur, atau dapat dilemparkan kepada pemain lain untuk memberikan informasi apakah informasi tersebut merupakan Hoax atau Fakta.

Jika pemain dapat memberikan jawaban yang benar, mereka akan mendapatkan 2 poin. Namun, jika pemain tidak dapat menjawab, 2 poin tersebut akan diberikan kepada pemain lain yang telah memberikan informasi.

Waktu menjawab paling lama 2 menit.

### Kartu Tebak Kata
Instruksi dari kartu ini adalah untuk menebak kata.

Sebuah kata akan ditebak dengan memilih salah satu dari 2 cara:

(Individu) Menebak 3 sinonim/antonim dari sebuah kata, atau;

(Kelompok) Menebak kata yang ditulis dan ditempelkan di kepala salah satu pemain. Pemain tersebut harus menebak kata tersebut dengan bantuan petunjuk dari teman-temannya dengan memberikan Clue.

Jika pemain dapat memberikan jawaban yang benar, mereka akan mendapatkan 4 poin, jika salah 0 poin.

Waktu menjawab paling lama 4 menit.


### Kartu Merangkai Cerita
Intruksi dari kartu ini adalah pemain mengambil kartu dan harus membuat cerita pendek atau kalimat yang menarik dari gambar di kartu tersebut.

Jika bermain secara kelompok, setiap pemain akan membuat kalimat yang kemudian disambungkan dengan kalimat dari teman kelompoknya, sehingga membentuk cerita yang menarik.

Poin yang dapat diterima:
Sempurna: 6 Poin
Bagus: 4 Poin
Lumayan: 2 Poin

Waktu menjawab paling lama 6 menit.

### Kartu Kolaborasi Berdampak
Instruksi dari kartu ini adalah memberikan tantangan yang harus diselesaikan bersama oleh seluruh kelompok.

Tantangan berupa kegiatan yang berdampak dilakukan bersama-sama dengan kelompok. Misalkan, mengumpulkan sampah disekitar area bermain atau kegiatan edukasi untuk anak-anak, dan sebagainya.

Poin yang dapat diterima:
Kompak: 10 Poin
Tidak main: 0 Poin

Waktu bermain paling lama 8 menit.

### Kartu Huruf Acak
Instruksi dari kartu ini adalah untuk menyusun sebuah kata dari huruf-huruf acak.

Huruf Acak akan disampaikan oleh instruktur atau pemain lain berdasarkan kartu yang diambil.

Jika pemain dapat memberikan jawaban yang benar, mereka akan mendapatkan 2 poin. Namun, jika pemain tidak dapat menjawab, maka tidak akan mendapatkan poin.

Waktu menjawab paling lama 3 menit.


## Board

```json
[
  // Baris 1 (Paling Bawah)
  [
    "START",
    "HOAX",
    "HURUF ACAK",
    "KOSONG",
    "KOSONG",
    "1+",
    "HOAX",
    "HURUF ACAK",
    "KOSONG",
    "REST AREA"
  ],
  // Baris 2
  [
    "HURUF ACAK",
    "MERANGKAI CERITA", 
    "KOSONG",
    "KOSONG",
    "CHALLENGE",
    "HOAX",
    "HURUF ACAK",
    "TEBAK KATA",
    "REST AREA"
  ],
  // Baris 3
  [
    "MERANGKAI CERITA", 
    "KOSONG",
    "KOSONG",
    "HOAX",
    "2+",
    "TEBAK KATA",
    "HOAX",
    "HURUF ACAK",
    "REST AREA"
  ],
  // Baris 4
  [
    "TEBAK KATA",
    "CHALLENGE", 
    "HURUF ACAK",
    "MERANGKAI CERITA",
    "1+",
    "KOSONG",
    "HOAX",
    "HURUF ACAK",
    "KOLABORASI BERDAMPAK",
  ],
  // Baris 5
  [
    "HOAX",
    "HURUF ACAK",
    "CHALLENGE",
    "MERANGKAI CERITA",
    "HOAX",
    "KOSONG",
    "HURUF ACAK",
    "KOSONG",
    "REST AREA"
  ],
  // Baris 6
  [
    "HURUF ACAK",
    "HOAX",
    "TEBAK KATA",
    "KOSONG",
    "2+",
    "MUNDUR WIR!!",
    "HOAX",
    "HURUF ACAK",
    "REST AREA"
  ],
  // Baris 7
  [
    "HOAX",
    "KOSONG",
    "TEBAK KATA",
    "MERANGKAI CERITA",
    "1+",
    "HURUF ACAK",
    "HOAX",
    "TEBAK KATA",
    "KOLABORASI BERDAMPAK",
  ],
  // Baris 8
  [
    "2-",
    "HOAX",
    "MUNDUR WIR!!",
    "TEBAK KATA",
    "MERANGKAI CERITA",
    "KOSONG",
    "HURUF ACAK",
    "HOAX",
    "REST AREA"
  ]
  // Baris 9
  [
    "TEBAK KATA",
    "MERANGKAI CERITA",
    "HOAX",
    "1+",
    "HURUF ACAK",
    "CHALLENGE",
    "MUNDUR WIR!!",
    "HOAX",
    "FINISH"
  ]
]
```