{"name": "literaksi", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test:e2e": "cypress run", "test:unit": "vitest", "lint": "eslint ."}, "dependencies": {"@capacitor-mlkit/barcode-scanning": "^7.2.1", "@capacitor/android": "^7.4.1", "@capacitor/app": "7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/core": "7.4.1", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@google/generative-ai": "^0.24.1", "@ionic/vue": "^8.0.0", "@ionic/vue-router": "^8.0.0", "ionicons": "^7.0.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@capacitor/cli": "7.4.1", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.3.0", "cypress": "^13.5.0", "eslint": "^8.35.0", "eslint-plugin-vue": "^9.9.0", "jsdom": "^22.1.0", "terser": "^5.4.0", "typescript": "~5.6.2", "vite": "~5.2.0", "vitest": "^0.34.6", "vue-tsc": "^2.1.10"}, "description": "An Ionic project"}