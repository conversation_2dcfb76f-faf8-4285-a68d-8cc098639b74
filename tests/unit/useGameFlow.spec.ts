import { describe, expect, test, vi, beforeEach } from 'vitest'
import { useGameFlow } from '@/composables/useGameFlow'
import { gameStateService } from '@/services/gameState'
import { geminiService } from '@/services/geminiService'

// Mock the services
vi.mock('@/services/gameState', () => ({
  gameStateService: {
    getGameState: vi.fn(),
    resetGame: vi.fn(),
    initializeGame: vi.fn(),
    nextTurn: vi.fn(),
    movePlayer: vi.fn(),
    getLeaderboard: vi.fn(),
    loadGameState: vi.fn()
  }
}))

vi.mock('@/services/geminiService', () => ({
  geminiService: {
    speak: vi.fn(),
    isSpeechAvailable: vi.fn(() => true)
  }
}))

vi.mock('@ionic/vue', () => ({
  toastController: {
    create: vi.fn(() => Promise.resolve({
      present: vi.fn()
    }))
  },
  alertController: {
    create: vi.fn(() => Promise.resolve({
      present: vi.fn()
    }))
  }
}))

describe('useGameFlow', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock return values
    vi.mocked(gameStateService.getGameState).mockReturnValue({
      gameId: 'test-game',
      players: [
        { id: 'player_1', name: 'Player 1', position: 0, score: 0, color: 'primary' },
        { id: 'player_2', name: 'Player 2', position: 5, score: 10, color: 'secondary' }
      ],
      currentPlayerIndex: 0,
      gameStarted: true,
      gameFinished: false,
      winner: null,
      currentTurn: 1,
      lastDiceRoll: null,
      currentCard: null,
      timerStartTime: null,
      timerDuration: null
    })
  })

  test('should initialize with correct game state', () => {
    const gameFlow = useGameFlow()
    
    expect(gameFlow.gameState.value.gameStarted).toBe(true)
    expect(gameFlow.gameState.value.players).toHaveLength(2)
    expect(gameFlow.currentPlayer.value.name).toBe('Player 1')
  })

  test('should handle exit game correctly', async () => {
    const gameFlow = useGameFlow()
    
    // Mock the alert controller to simulate user clicking "Ya, Keluar"
    const mockAlert = {
      present: vi.fn(),
      buttons: []
    }
    
    const { alertController } = await import('@ionic/vue')
    vi.mocked(alertController.create).mockResolvedValue(mockAlert as any)
    
    await gameFlow.exitGame()
    
    expect(alertController.create).toHaveBeenCalledWith({
      header: 'Keluar dari Permainan',
      message: 'Yakin ingin keluar dari permainan? Semua progress akan hilang.',
      buttons: expect.arrayContaining([
        expect.objectContaining({
          text: 'Batal',
          role: 'cancel',
          cssClass: 'secondary'
        }),
        expect.objectContaining({
          text: 'Ya, Keluar',
          cssClass: 'danger',
          handler: expect.any(Function)
        })
      ])
    })
  })

  test('should handle reset game correctly', async () => {
    const gameFlow = useGameFlow()
    
    // Mock the alert controller to simulate user clicking "Ya"
    const mockAlert = {
      present: vi.fn(),
      buttons: []
    }
    
    const { alertController } = await import('@ionic/vue')
    vi.mocked(alertController.create).mockResolvedValue(mockAlert as any)
    
    await gameFlow.resetGame()
    
    expect(alertController.create).toHaveBeenCalledWith({
      header: 'Reset Permainan',
      message: 'Yakin ingin memulai permainan baru?',
      buttons: expect.arrayContaining([
        'Batal',
        expect.objectContaining({
          text: 'Ya'
        })
      ])
    })
  })

  test('should add and remove players correctly', () => {
    const gameFlow = useGameFlow()
    
    // Test adding player
    gameFlow.newPlayerName.value = 'New Player'
    gameFlow.addPlayer()
    
    expect(gameFlow.playerNames.value).toContain('New Player')
    expect(gameFlow.newPlayerName.value).toBe('')
    
    // Test removing player
    const initialLength = gameFlow.playerNames.value.length
    gameFlow.removePlayer(0)
    
    expect(gameFlow.playerNames.value).toHaveLength(initialLength - 1)
  })

  test('should not add empty player names', () => {
    const gameFlow = useGameFlow()
    
    gameFlow.newPlayerName.value = '   '
    const initialLength = gameFlow.playerNames.value.length
    gameFlow.addPlayer()
    
    expect(gameFlow.playerNames.value).toHaveLength(initialLength)
  })

  test('should not add more than 8 players', () => {
    const gameFlow = useGameFlow()
    
    // Fill with 8 players
    for (let i = 0; i < 8; i++) {
      gameFlow.newPlayerName.value = `Player ${i + 1}`
      gameFlow.addPlayer()
    }
    
    // Try to add 9th player
    gameFlow.newPlayerName.value = 'Player 9'
    gameFlow.addPlayer()
    
    expect(gameFlow.playerNames.value).toHaveLength(8)
  })

  test('should calculate canProceedToNextTurn correctly', () => {
    const gameFlow = useGameFlow()

    // Initially should not be able to proceed (no dice roll)
    expect(gameFlow.canProceedToNextTurn.value).toBe(false)

    // Mock game state with dice roll but no current card
    vi.mocked(gameStateService.getGameState).mockReturnValue({
      ...gameFlow.gameState.value,
      lastDiceRoll: 3,
      currentCard: null
    })

    // Re-initialize to get updated state
    const updatedGameFlow = useGameFlow()
    expect(updatedGameFlow.canProceedToNextTurn.value).toBe(true)
  })

  test('should display leaderboard correctly', () => {
    const mockLeaderboard = [
      { id: 'player_2', name: 'Player 2', position: 5, score: 15, color: 'secondary' },
      { id: 'player_1', name: 'Player 1', position: 3, score: 10, color: 'primary' },
      { id: 'player_3', name: 'Player 3', position: 1, score: 5, color: 'tertiary' }
    ]

    vi.mocked(gameStateService.getLeaderboard).mockReturnValue(mockLeaderboard)

    const gameFlow = useGameFlow()

    expect(gameFlow.leaderboard.value).toEqual(mockLeaderboard)
    expect(gameFlow.leaderboard.value[0].name).toBe('Player 2') // Highest score first
    expect(gameFlow.leaderboard.value[0].score).toBe(15)
  })

  test('should handle game finished state', () => {
    vi.mocked(gameStateService.getGameState).mockReturnValue({
      gameId: 'test-game',
      players: [
        { id: 'player_1', name: 'Player 1', position: 89, score: 25, color: 'primary' },
        { id: 'player_2', name: 'Player 2', position: 5, score: 10, color: 'secondary' }
      ],
      currentPlayerIndex: 0,
      gameStarted: true,
      gameFinished: true,
      winner: 'player_1',
      currentTurn: 10,
      lastDiceRoll: null,
      currentCard: null,
      timerStartTime: null,
      timerDuration: null
    })

    const gameFlow = useGameFlow()

    expect(gameFlow.gameState.value.gameFinished).toBe(true)
    expect(gameFlow.gameState.value.winner).toBe('player_1')
  })
})
