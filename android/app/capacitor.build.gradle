// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-mlkit-barcode-scanning')
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-status-bar')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
