/* For information on how to create your own theme, please see:
http://ionicframework.com/docs/theming/ */

/* ===== LITERAKSI COLORFUL THEME ===== */

/* Custom Color Variables */
:root {
  /* Primary Colors - Vibrant and Fun */
  --literaksi-primary: #4A90E2;
  --literaksi-primary-light: #6BA3E8;
  --literaksi-primary-dark: #3A7BC8;

  --literaksi-secondary: #FF8C42;
  --literaksi-secondary-light: #FFB366;
  --literaksi-secondary-dark: #E67A2E;

  --literaksi-success: #2ECC71;
  --literaksi-success-light: #58D68D;
  --literaksi-success-dark: #27AE60;

  --literaksi-warning: #F1C40F;
  --literaksi-warning-light: #F4D03F;
  --literaksi-warning-dark: #D4AC0D;

  --literaksi-danger: #E74C3C;
  --literaksi-danger-light: #EC7063;
  --literaksi-danger-dark: #C0392B;

  --literaksi-purple: #9B59B6;
  --literaksi-purple-light: #BB8FCE;
  --literaksi-purple-dark: #8E44AD;

  /* Card Type Colors */
  --card-hoax-start: #E74C3C;
  --card-hoax-end: #C0392B;
  --card-huruf-start: #3498DB;
  --card-huruf-end: #2980B9;
  --card-tebak-start: #9B59B6;
  --card-tebak-end: #8E44AD;
  --card-cerita-start: #27AE60;
  --card-cerita-end: #229954;
  --card-kolaborasi-start: #E67E22;
  --card-kolaborasi-end: #D35400;

  /* Background Gradients */
  --bg-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --bg-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --bg-gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --bg-gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* Fun Shadows */
  --shadow-colorful: 0 4px 15px rgba(74, 144, 226, 0.3);
  --shadow-warm: 0 4px 15px rgba(255, 140, 66, 0.3);
  --shadow-success: 0 4px 15px rgba(46, 204, 113, 0.3);
  --shadow-purple: 0 4px 15px rgba(155, 89, 182, 0.3);
}

/* Override Ionic Colors with our vibrant palette */
:root {
  --ion-color-primary: #4A90E2;
  --ion-color-primary-rgb: 74, 144, 226;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #4180c7;
  --ion-color-primary-tint: #5c9de5;

  --ion-color-secondary: #FF8C42;
  --ion-color-secondary-rgb: 255, 140, 66;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #e07b3a;
  --ion-color-secondary-tint: #ff9855;

  --ion-color-tertiary: #9B59B6;
  --ion-color-tertiary-rgb: 155, 89, 182;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #884ea0;
  --ion-color-tertiary-tint: #a56abd;

  --ion-color-success: #2ECC71;
  --ion-color-success-rgb: 46, 204, 113;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #29b464;
  --ion-color-success-tint: #43d17f;

  --ion-color-warning: #F1C40F;
  --ion-color-warning-rgb: 241, 196, 15;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #d4ac0d;
  --ion-color-warning-tint: #f2ca27;

  --ion-color-danger: #E74C3C;
  --ion-color-danger-rgb: 231, 76, 60;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cb4335;
  --ion-color-danger-tint: #ea6250;
}

/* Fix header overlap issues */
ion-header {
  position: relative;
  z-index: 10;
  background: var(--bg-gradient-primary);
}

ion-content {
  --offset-top: 0;
  --background: linear-gradient(180deg, #f8f9ff 0%, #e8f4fd 100%);
}

/* Ensure proper spacing for mobile devices */
@media (max-width: 768px) {
  ion-header ion-toolbar {
    --min-height: 56px;
  }
}

/* ===== FUN UI ENHANCEMENTS ===== */

/* Colorful Buttons */
.btn-colorful {
  background: var(--bg-gradient-primary);
  color: white;
  border-radius: 25px;
  box-shadow: var(--shadow-colorful);
  transition: all 0.3s ease;
  border: none;
  font-weight: 600;
}

.btn-colorful:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.btn-warm {
  background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
  box-shadow: var(--shadow-warm);
}

.btn-success {
  background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
  box-shadow: var(--shadow-success);
}

.btn-purple {
  background: linear-gradient(135deg, var(--literaksi-purple), var(--literaksi-purple-dark));
  box-shadow: var(--shadow-purple);
}

/* Fun Card Styles */
.card-colorful {
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.card-colorful:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.card-gradient-border {
  position: relative;
  background: white;
  border-radius: 20px;
}

.card-gradient-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--bg-gradient-primary);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
}

/* Player Avatar Enhancements */
.player-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.player-avatar:hover {
  transform: scale(1.1);
}

.player-avatar.primary {
  background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
}

.player-avatar.secondary {
  background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
}

.player-avatar.success {
  background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
}

.player-avatar.warning {
  background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
}

.player-avatar.danger {
  background: linear-gradient(135deg, var(--literaksi-danger), var(--literaksi-danger-dark));
}

.player-avatar.purple {
  background: linear-gradient(135deg, var(--literaksi-purple), var(--literaksi-purple-dark));
}

/* Animated Elements */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse-colorful {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
  }
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse-colorful 2s infinite;
}

/* Fun Text Styles */
.text-gradient {
  background: var(--bg-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

.text-shadow-fun {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}
