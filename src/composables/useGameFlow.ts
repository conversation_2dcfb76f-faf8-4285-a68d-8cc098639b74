import { ref, computed } from 'vue';
import { toast<PERSON><PERSON>roller, alertController } from '@ionic/vue';
import { gameStateService, type GameState, type Player } from '../services/gameState';
import { geminiService } from '../services/geminiService';
import { voiceInputService } from '../services/voiceInputService';

export function useGameFlow() {
  // Reactive state
  const gameState = ref<GameState>(gameStateService.getGameState());
  const playerNames = ref<string[]>([]);
  const newPlayerName = ref('');
  const isProcessing = ref(false);

  // Computed properties
  const currentPlayer = computed((): Player => {
    return gameState.value.players[gameState.value.currentPlayerIndex] || {
      id: '',
      name: '',
      position: 0,
      score: 0,
      color: 'primary'
    };
  });

  const leaderboard = computed((): Player[] => {
    // Make sure this computed property reacts to gameState changes
    return [...gameState.value.players].sort((a, b) => b.score - a.score);
  });

  const canProceedToNextTurn = computed((): boolean => {
    return !gameState.value.currentCard && gameState.value.lastDiceRoll !== null && !isProcessing.value;
  });

  // Game setup functions
  const addPlayer = (): void => {
    if (newPlayerName.value.trim() && playerNames.value.length < 8) {
      playerNames.value.push(newPlayerName.value.trim());
      newPlayerName.value = '';
    }
  };

  const removePlayer = (index: number): void => {
    playerNames.value.splice(index, 1);
  };

  const loadGameState = async (): Promise<void> => {
    await gameStateService.loadGameState();
    gameState.value = gameStateService.getGameState();
  };

  // Game flow functions
  const initializeGameDirectly = async (): Promise<void> => {
    try {
      await gameStateService.initializeGame(playerNames.value);
      gameState.value = gameStateService.getGameState();
      
      const toast = await toastController.create({
        message: '🎮 Permainan LiterAksi dimulai!',
        duration: 2000,
        color: 'success'
      });
      await toast.present();
    } catch (error) {
      console.error('Error starting game:', error);
    }
  };

  const nextTurn = async (): Promise<void> => {
    await gameStateService.nextTurn();
    gameState.value = gameStateService.getGameState();
    
    if (geminiService.isSpeechAvailable()) {
      await geminiService.speak(`Giliran ${currentPlayer.value.name}`);
    }
  };

  const exitGame = async (): Promise<void> => {
    const alert = await alertController.create({
      header: 'Keluar dari Permainan',
      message: 'Yakin ingin keluar dari permainan? Semua progress akan hilang.',
      buttons: [
        {
          text: 'Batal',
          role: 'cancel',
          cssClass: 'secondary'
        },
        {
          text: 'Ya, Keluar',
          cssClass: 'danger',
          handler: async () => {
            // Reset game state
            await gameStateService.resetGame();
            gameState.value = gameStateService.getGameState();
            playerNames.value = [];
          }
        }
      ]
    });
    await alert.present();
  };

  const resetGame = async (): Promise<void> => {
    const alert = await alertController.create({
      header: 'Reset Permainan',
      message: 'Yakin ingin memulai permainan baru?',
      buttons: [
        'Batal',
        {
          text: 'Ya',
          handler: async () => {
            await gameStateService.resetGame();
            gameState.value = gameStateService.getGameState();
            playerNames.value = [];
          }
        }
      ]
    });
    await alert.present();
  };

  // Player movement functions
  const movePlayerAndCheckSquare = async (): Promise<void> => {
    if (!gameState.value.lastDiceRoll) return;

    isProcessing.value = true;
    try {
      // Move player step by step
      await movePlayerStepByStep(currentPlayer.value.id, gameState.value.lastDiceRoll);

      // Check for special squares
      const position = currentPlayer.value.position;
      await checkSpecialSquare(position);

    } catch (error) {
      console.error('Error moving player:', error);
    } finally {
      isProcessing.value = false;
    }
  };

  // Move player one step at a time with delay
  const movePlayerStepByStep = async (playerId: string, totalSteps: number): Promise<void> => {
    for (let step = 1; step <= totalSteps; step++) {
      // Move one step
      await gameStateService.movePlayer(playerId, 1);

      // Force reactive update by creating a new reference
      gameState.value = { ...gameStateService.getGameState() };

      // Add delay between steps (300ms for better UX)
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  };

  const checkSpecialSquare = async (position: number): Promise<void> => {
    const { cardService } = await import('../services/cardService');
    const squareInfo = cardService.getBoardSquareInfo(position);

    console.log('Checking special square at position:', position, 'Type:', squareInfo.type);

    // Handle card-based squares
    if (cardService.requiresCard(position)) {
      const cardType = squareInfo.type;

      // Automatically generate and set a random card for this type
      const randomCard = cardService.getRandomCardByType(cardType);

      if (randomCard) {
        // Set the card in game state
        await gameStateService.setCurrentCard(randomCard);
        gameState.value = gameStateService.getGameState();

        // Announce the challenge
        if (geminiService.isSpeechAvailable()) {
          const cardTitle = cardService.getCardTypeTitle(cardType);
          await geminiService.speak(`${currentPlayer.value.name} mendapat tantangan ${cardTitle}!`);
        }

        // Show toast notification
        const toast = await toastController.create({
          message: `🎯 Tantangan ${cardService.getCardTypeTitle(cardType)} muncul!`,
          duration: 3000,
          color: 'primary'
        });
        await toast.present();

        // Start timer for the challenge
        const timeLimit = cardService.getTimeLimit(cardType);
        await gameStateService.startTimer(timeLimit * 1000); // Convert to milliseconds
      } else {
        console.error('No cards available for type:', cardType);

        // Fallback: show error message
        const toast = await toastController.create({
          message: `❌ Tidak ada kartu tersedia untuk ${cardService.getCardTypeTitle(cardType)}`,
          duration: 3000,
          color: 'danger'
        });
        await toast.present();
      }
    } else {
      // Handle other special squares
      await handleNonCardSquare(squareInfo);
    }
  };

  const handleNonCardSquare = async (squareInfo: { type: string, description: string }): Promise<void> => {
    const { type } = squareInfo;

    switch (type) {
      case '1+':
        await gameStateService.addScore(currentPlayer.value.id, 1);
        if (geminiService.isSpeechAvailable()) {
          await geminiService.speak(`${currentPlayer.value.name} mendapat bonus 1 poin!`);
        }
        break;

      case '2+':
        await gameStateService.addScore(currentPlayer.value.id, 2);
        if (geminiService.isSpeechAvailable()) {
          await geminiService.speak(`${currentPlayer.value.name} mendapat bonus 2 poin!`);
        }
        break;

      case '2-':
        await gameStateService.addScore(currentPlayer.value.id, -2);
        if (geminiService.isSpeechAvailable()) {
          await geminiService.speak(`${currentPlayer.value.name} kehilangan 2 poin!`);
        }
        break;

      case 'REST AREA':
        if (geminiService.isSpeechAvailable()) {
          await geminiService.speak(`${currentPlayer.value.name} beristirahat. Sebutkan kutipan motivasi!`);
        }
        break;

      case 'MUNDUR WIR!!':
        const lastRoll = gameState.value.lastDiceRoll || 1;
        const newPosition = Math.max(0, currentPlayer.value.position - lastRoll);
        await gameStateService.movePlayerToPosition(currentPlayer.value.id, newPosition);
        if (geminiService.isSpeechAvailable()) {
          await geminiService.speak(`${currentPlayer.value.name} mundur ${lastRoll} langkah!`);
        }
        break;

      case 'CHALLENGE':
        if (geminiService.isSpeechAvailable()) {
          await geminiService.speak(`${currentPlayer.value.name} mendapat tantangan khusus dari instruktur!`);
        }
        break;

      case 'FINISH':
        if (!gameState.value.gameFinished) {
          gameState.value.gameFinished = true;
          gameState.value.winner = currentPlayer.value.id;
          await gameStateService.addScore(currentPlayer.value.id, 10);
          if (geminiService.isSpeechAvailable()) {
            await geminiService.speak(`Selamat! ${currentPlayer.value.name} memenangkan permainan!`);
          }
        }
        break;
    }

    // Update game state after handling special square
    gameState.value = gameStateService.getGameState();
  };

  // Winner functions
  const getWinnerName = (): string => {
    const winner = gameState.value.players.find(p => p.id === gameState.value.winner);
    return winner?.name || 'Unknown';
  };

  // Card completion handler
  const completeCardAndNextTurn = async (): Promise<void> => {
    // Clear current card
    await gameStateService.setCurrentCard(null);

    // Move to next turn
    await nextTurn();
  };

  return {
    // State
    gameState,
    playerNames,
    newPlayerName,
    isProcessing,

    // Computed
    currentPlayer,
    leaderboard,
    canProceedToNextTurn,

    // Functions
    addPlayer,
    removePlayer,
    loadGameState,
    initializeGameDirectly,
    nextTurn,
    exitGame,
    resetGame,
    movePlayerAndCheckSquare,
    getWinnerName,
    completeCardAndNextTurn
  };
}
