import { ref, onMounted, onUnmounted } from 'vue';
import { toastController } from '@ionic/vue';
import { indonesianTTSService, type TTSEvent } from '../services/indonesianTTSService';
import { settingsService } from '../services/settingsService';

export function useSpeech() {
  // TTS Settings
  const selectedTTSProvider = ref('Auto');
  const autoFallbackEnabled = ref(true);
  const availableTTSProviders = ref(indonesianTTSService.getAvailableProviders());

  // Subtitle Settings
  const subtitlesEnabled = ref(true);
  const currentSubtitle = ref('');
  const isShowingSubtitle = ref(false);

  // TTS event handler for subtitles
  const handleTTSEvent = (event: TTSEvent): void => {
    if (!subtitlesEnabled.value) return;
    
    switch (event.type) {
      case 'start':
        currentSubtitle.value = event.text || '';
        isShowingSubtitle.value = true;
        break;
      case 'end':
        isShowingSubtitle.value = false;
        currentSubtitle.value = '';
        break;
      case 'error':
        isShowingSubtitle.value = false;
        currentSubtitle.value = '';
        break;
    }
  };

  // TTS Provider functions
  const onTTSProviderChange = async (event: CustomEvent): Promise<void> => {
    const newProvider = event.detail.value;
    const oldProvider = selectedTTSProvider.value;

    // Update reactive state immediately
    selectedTTSProvider.value = newProvider;

    // Update the service
    indonesianTTSService.setTTSProvider(newProvider);

    // Update available providers list to reflect any changes
    availableTTSProviders.value = indonesianTTSService.getAvailableProviders();

    // Get current provider info for better feedback
    const currentProviderInfo = indonesianTTSService.getCurrentProviderInfo();
    const actualProvider = currentProviderInfo?.name || 'Unknown';

    // Show toast with actual provider being used
    const message = newProvider === 'Auto'
      ? `TTS provider: Auto (menggunakan ${actualProvider})`
      : `TTS provider diubah ke: ${newProvider}${actualProvider !== newProvider ? ` (menggunakan ${actualProvider})` : ''}`;

    const toast = await toastController.create({
      message,
      duration: 3000,
      color: 'primary'
    });
    await toast.present();

    console.log(`TTS provider changed from ${oldProvider} to ${newProvider}, using ${actualProvider}`);
  };

  const onAutoFallbackChange = (event: CustomEvent): void => {
    const enabled = event.detail.checked;
    autoFallbackEnabled.value = enabled;
    indonesianTTSService.setAutoFallback(enabled);
    
    const toast = toastController.create({
      message: `Auto fallback ${enabled ? 'diaktifkan' : 'dinonaktifkan'}`,
      duration: 2000,
      color: 'primary'
    });
    toast.then(t => t.present());
  };

  // Subtitle functions
  const onSubtitlesChange = (event: CustomEvent): void => {
    const enabled = event.detail.checked;
    subtitlesEnabled.value = enabled;
    settingsService.setSubtitlesEnabled(enabled);
    
    const toast = toastController.create({
      message: `Subtitle ${enabled ? 'diaktifkan' : 'dinonaktifkan'}`,
      duration: 2000,
      color: 'primary'
    });
    toast.then(t => t.present());
  };

  // Test functions
  const testIndonesianTTSService = async (): Promise<void> => {
    try {
      const isWorking = await indonesianTTSService.testIndonesianSpeech();
      
      if (isWorking) {
        const toast = await toastController.create({
          message: `🎤 TTS Indonesia berhasil! Provider: ${getCurrentTTSProvider()}`,
          duration: 3000,
          color: 'success'
        });
        await toast.present();
      } else {
        const toast = await toastController.create({
          message: '❌ TTS Indonesia gagal. Coba provider lain.',
          duration: 3000,
          color: 'danger'
        });
        await toast.present();
      }
    } catch (error) {
      console.error('TTS test failed:', error);
      const toast = await toastController.create({
        message: 'Error testing TTS: ' + (error as Error).message,
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    }
  };

  // Test current provider specifically
  const testCurrentProvider = async (): Promise<void> => {
    try {
      const providerInfo = indonesianTTSService.getCurrentProviderInfo();
      if (!providerInfo) {
        throw new Error('No provider selected');
      }

      const testText = `Tes provider ${providerInfo.name}. Suara Indonesia berhasil!`;
      await indonesianTTSService.speak(testText);

      const toast = await toastController.create({
        message: `✅ Provider ${providerInfo.name} berfungsi dengan baik!`,
        duration: 3000,
        color: 'success'
      });
      await toast.present();
    } catch (error) {
      console.error('Provider test failed:', error);

      const toast = await toastController.create({
        message: '❌ Tes provider gagal',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    }
  };

  const reloadIndonesianVoice = async (): Promise<void> => {
    try {
      // Force reload of TTS providers
      availableTTSProviders.value = indonesianTTSService.getAvailableProviders();
      
      const toast = await toastController.create({
        message: '🔄 Suara Indonesia dimuat ulang',
        duration: 2000,
        color: 'primary'
      });
      await toast.present();
    } catch (error) {
      console.error('Voice reload failed:', error);
    }
  };

  // Helper functions
  const getCurrentTTSProvider = (): string => {
    return indonesianTTSService.getCurrentProvider() || 'Browser Default';
  };

  const getCurrentVoice = (): any => {
    // This would return current voice info if available
    return null;
  };

  // Load settings on mount
  const loadSettings = (): void => {
    // Load TTS settings
    const ttsSettings = indonesianTTSService.getTTSSettings();
    selectedTTSProvider.value = ttsSettings.preferredProvider;
    autoFallbackEnabled.value = ttsSettings.autoFallback;

    // Load subtitle settings
    subtitlesEnabled.value = settingsService.getSubtitlesEnabled();

    // Set up TTS event listener for subtitles
    indonesianTTSService.addEventListener(handleTTSEvent);
  };

  // Cleanup on unmount
  const cleanup = (): void => {
    indonesianTTSService.removeEventListener(handleTTSEvent);
  };

  // Lifecycle hooks
  onMounted(() => {
    loadSettings();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    // TTS State
    selectedTTSProvider,
    autoFallbackEnabled,
    availableTTSProviders,
    
    // Subtitle State
    subtitlesEnabled,
    currentSubtitle,
    isShowingSubtitle,
    
    // Functions
    onTTSProviderChange,
    onAutoFallbackChange,
    onSubtitlesChange,
    testIndonesianTTSService,
    testCurrentProvider,
    reloadIndonesianVoice,
    getCurrentTTSProvider,
    getCurrentVoice,
    loadSettings,
    cleanup
  };
}
