import { ref } from 'vue';
import { toastController } from '@ionic/vue';
import { voiceInputService } from '../services/voiceInputService';
import { gameStateService } from '../services/gameState';
import { geminiService } from '../services/geminiService';

export function useVoiceInput() {
  // Voice input state
  const isListening = ref(false);
  const voiceTranscript = ref('');
  const isListeningForDice = ref(false);
  const diceVoiceTranscript = ref('');

  // Voice dice input functions
  const startVoiceDiceInput = async (movePlayerCallback?: () => Promise<void>): Promise<void> => {
    try {
      isListeningForDice.value = true;
      diceVoiceTranscript.value = '';

      // Announce instruction
      if (geminiService.isSpeechAvailable()) {
        await geminiService.speak('Sebutkan angka dadu dari satu sampai enam');
      }

      const diceNumber = await voiceInputService.listenForDiceNumber();

      if (diceNumber >= 1 && diceNumber <= 6) {
        // Valid dice number
        diceVoiceTranscript.value = `Angka ${diceNumber}`;
        await gameStateService.setDiceRoll(diceNumber);

        // Announce result
        if (geminiService.isSpeechAvailable()) {
          const currentPlayer = gameStateService.getGameState().players[gameStateService.getGameState().currentPlayerIndex];
          await geminiService.speak(`${currentPlayer.name} mendapat angka ${diceNumber}!`);
        }

        // Automatically move player if callback is provided
        if (movePlayerCallback) {
          await movePlayerCallback();
        }
      } else {
        // Invalid or no number recognized
        diceVoiceTranscript.value = 'Angka tidak terdengar jelas';
        const toast = await toastController.create({
          message: 'Angka tidak terdengar jelas. Coba lagi dengan jelas: "satu", "dua", "tiga", "empat", "lima", atau "enam"',
          duration: 4000,
          color: 'warning'
        });
        await toast.present();
      }
    } catch (error) {
      console.error('Voice dice input error:', error);
      const toast = await toastController.create({
        message: 'Error dalam voice input: ' + (error as Error).message,
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      isListeningForDice.value = false;
    }
  };

  const stopVoiceDiceInput = (): void => {
    voiceInputService.stopListening();
    isListeningForDice.value = false;
    diceVoiceTranscript.value = '';
  };

  // Card voice input functions
  const startVoiceInput = async (cardType?: string): Promise<void> => {
    try {
      isListening.value = true;
      voiceTranscript.value = '';
      
      let transcript = '';
      
      switch (cardType) {
        case 'HOAX':
          transcript = await voiceInputService.listenForHoaxFaktaAnswer();
          break;
        case 'HURUF ACAK':
        case 'TEBAK KATA':
          transcript = await voiceInputService.listenForWordAnswer();
          break;
        case 'MERANGKAI CERITA':
          transcript = await voiceInputService.listenForStory();
          break;
        default:
          transcript = await voiceInputService.listenForWordAnswer();
      }
      
      voiceTranscript.value = transcript;
    } catch (error) {
      console.error('Voice input error:', error);
      const toast = await toastController.create({
        message: 'Error dalam voice input: ' + (error as Error).message,
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    } finally {
      isListening.value = false;
    }
  };

  const stopVoiceInput = (): void => {
    voiceInputService.stopListening();
    isListening.value = false;
  };

  // Clear voice input state
  const clearVoiceInput = (): void => {
    voiceTranscript.value = '';
    diceVoiceTranscript.value = '';
    isListening.value = false;
    isListeningForDice.value = false;
    voiceInputService.stopListening();
  };

  return {
    // State
    isListening,
    voiceTranscript,
    isListeningForDice,
    diceVoiceTranscript,
    
    // Functions
    startVoiceDiceInput,
    stopVoiceDiceInput,
    startVoiceInput,
    stopVoiceInput,
    clearVoiceInput
  };
}
