import { ref, computed } from 'vue';
import { toast<PERSON>ontroller } from '@ionic/vue';
import { cardService } from '../services/cardService';
import { gameStateService } from '../services/gameState';
import { geminiService } from '../services/geminiService';

export function useCardChallenge() {
  const answerInputMethod = ref<'voice' | 'manual'>('voice');
  const manualAnswer = ref('');
  const currentInstruction = ref('');

  const canSubmitAnswer = computed((): boolean => {
    if (answerInputMethod.value === 'voice') {
      // This will be passed from the parent component that has access to voiceTranscript
      return false; // Will be overridden by parent
    } else {
      return manualAnswer.value.trim().length > 0;
    }
  });

  const getCardDuration = (cardType: string): number => {
    const durations: { [key: string]: number } = {
      'HOAX': 120, // 2 minutes
      'HURUF ACAK': 180, // 3 minutes
      'TEBAK KATA': 240, // 4 minutes
      'MERANGKAI CERITA': 360, // 6 minutes
      'KOLABORASI BERDAMPAK': 480 // 8 minutes
    };
    return durations[cardType] || 180;
  };

  const getCardTypeTitle = (cardType: string): string => {
    const titles: { [key: string]: string } = {
      'HOAX': 'Hoax atau Fakta',
      'HURUF ACAK': 'Huruf Acak',
      'TEBAK KATA': 'Tebak Kata',
      'MERANGKAI CERITA': 'Merangkai Cerita',
      'KOLABORASI BERDAMPAK': 'Kolaborasi Berdampak'
    };
    return titles[cardType] || cardType;
  };

  const updateCardInstruction = (card: any) => {
    if (!card) {
      currentInstruction.value = '';
      return;
    }

    // Set the current instruction based on card type and data
    switch (card.type) {
      case 'HOAX':
        currentInstruction.value = card.question;
        break;
      case 'HURUF ACAK':
        currentInstruction.value = `Susun huruf berikut menjadi kata yang bermakna: ${card.scrambled}`;
        break;
      case 'TEBAK KATA':
        currentInstruction.value = `Sebutkan ${card.type === 'sinonim' ? 'sinonim' : 'antonim'} dari kata: ${card.word}. Hint: ${card.hint}`;
        break;
      case 'MERANGKAI CERITA':
        currentInstruction.value = card.prompt;
        break;
      case 'KOLABORASI BERDAMPAK':
        currentInstruction.value = card.description;
        break;
      default:
        currentInstruction.value = cardService.getCardInstructions(card);
    }
  };

  const processAnswer = async (
    answer: string,
    currentCard: any,
    currentPlayer: any,
    onComplete: () => Promise<void>
  ): Promise<void> => {
    if (!currentCard || !answer.trim()) return;

    try {
      const card = currentCard;

      // Evaluate the answer based on card type
      let evaluation;
      switch (card.cardType) {
        case 'HOAX':
          evaluation = cardService.evaluateHoaxFakta(card, answer);
          break;
        case 'HURUF ACAK':
          evaluation = cardService.evaluateHurufAcak(card, answer);
          break;
        case 'TEBAK KATA':
          // Split answer by comma or space for multiple answers
          const answers = answer.split(/[,\s]+/).filter(a => a.trim().length > 0);
          evaluation = cardService.evaluateTebakKata(card, answers);
          break;
        case 'MERANGKAI CERITA':
          evaluation = cardService.evaluateMerangkaiCerita(answer);
          break;
        case 'KOLABORASI BERDAMPAK':
          evaluation = { score: 5, feedback: 'Terima kasih atas partisipasinya!', correct: true };
          break;
        default:
          evaluation = { score: 0, feedback: 'Tipe kartu tidak dikenali', correct: false };
      }

      // Add score to current player
      if (evaluation.score > 0) {
        await gameStateService.addScore(currentPlayer.id, evaluation.score);
      }

      // Show feedback
      const toast = await toastController.create({
        message: `${evaluation.correct ? '✅' : '❌'} ${evaluation.feedback} (+${evaluation.score} poin)`,
        duration: 4000,
        color: evaluation.correct ? 'success' : 'warning'
      });
      await toast.present();

      // Announce result if speech is available
      if (geminiService.isSpeechAvailable()) {
        await geminiService.speak(`${evaluation.feedback}. ${currentPlayer.name} mendapat ${evaluation.score} poin.`);
      }

      // Clear inputs
      manualAnswer.value = '';

      // Complete card and move to next turn automatically
      await onComplete();

    } catch (error) {
      console.error('Error processing answer:', error);
      const toast = await toastController.create({
        message: 'Terjadi kesalahan saat memproses jawaban',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    }
  };

  const skipChallenge = async (): Promise<void> => {
    await gameStateService.setCurrentCard(null);
    manualAnswer.value = '';
  };

  const handleAnswerInputMethodChange = (clearVoiceInput: () => void): void => {
    // Clear both inputs when switching methods
    manualAnswer.value = '';
    clearVoiceInput();
  };

  const clearInputs = (clearVoiceInput: () => void): void => {
    manualAnswer.value = '';
    clearVoiceInput();
  };

  return {
    answerInputMethod,
    manualAnswer,
    currentInstruction,
    canSubmitAnswer,
    getCardDuration,
    getCardTypeTitle,
    updateCardInstruction,
    processAnswer,
    skipChallenge,
    handleAnswerInputMethodChange,
    clearInputs
  };
}
