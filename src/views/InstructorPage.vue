<template>
  <ion-page>
    <ion-header>
      <ion-toolbar>
        <ion-title>LiterAksi - Instructor</ion-title>
        <ion-buttons slot="end">
          <!-- Game <PERSON><PERSON> (only show during game) -->
          <ion-button v-if="gameState.gameStarted && !gameState.gameFinished" @click="openGameMenu">
            <ion-icon :icon="ellipsisVertical"></ion-icon>
          </ion-button>
          <ion-button @click="openSettings">
            <ion-icon :icon="settings"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <!-- Game Setup Section -->
      <GameSetup
        v-if="!gameState.gameStarted"
        v-model:playerNames="playerNames"
        v-model:newPlayerName="newPlayerName"
        v-model:geminiInitialized="geminiInitialized"
        @startGame="startGame"
      />

      <!-- Game In Progress Section -->
      <div v-else-if="!gameState.gameFinished" class="game-section">
        <!-- Current Player Info -->
        <CurrentPlayerCard
          :current-player="currentPlayer"
          :current-turn="gameState.currentTurn"
        />

        <!-- Subtitle Display -->
        <SubtitleCard
          :is-showing-subtitle="isShowingSubtitle"
          :subtitles-enabled="subtitlesEnabled"
          :current-subtitle="currentSubtitle"
        />

        <!-- Game Controls -->
        <ion-card class="game-controls-card">
          <ion-card-content>
            <div class="control-section">
              <!-- Voice Dice Input -->
              <VoiceDiceInput
                v-if="!gameState.lastDiceRoll"
                :isListeningForDice="isListeningForDice"
                :diceVoiceTranscript="diceVoiceTranscript"
                :isProcessing="isProcessing"
                @startVoiceInput="handleStartVoiceDiceInput"
                @stopVoiceInput="stopVoiceDiceInput"
                @manualDiceInput="handleManualDiceInput"
              />

              <!-- Dice Result -->
              <DiceResultDisplay
                :dice-roll="gameState.lastDiceRoll"
                :is-processing="isProcessing"
              />

              <!-- Card Section -->
              <CardChallengeSection
                v-if="currentCard"
                :current-card="currentCard"
                :is-listening="isListening"
                :voice-transcript="voiceTranscript"
                :current-player="currentPlayer"
                @start-voice-input="startCardVoiceInput"
                @stop-voice-input="stopVoiceInput"
                @timer-finish="handleTimerFinish"
                @warning-sound="playWarningSound"
                @danger-sound="playDangerSound"
                @complete-card="completeCardAndNextTurn"
              />

              <!-- Auto-challenge message -->
              <div v-if="showQRScanner" class="auto-challenge-section">
                <ion-note color="medium">
                  <ion-icon :icon="informationCircle" class="info-icon"></ion-icon>
                  Tantangan akan muncul otomatis saat mendarat di petak kartu
                </ion-note>
              </div>

              <!-- Next Turn Section -->
              <div v-if="canProceedToNextTurn" class="next-turn-section">
                <ion-button
                  expand="block"
                  @click="nextTurn"
                  color="success"
                >
                  <ion-icon :icon="checkmark" slot="start"></ion-icon>
                  Giliran Selanjutnya
                </ion-button>
              </div>

              <!-- Quick Game Actions -->
              <div class="quick-actions">
                <ion-button
                  expand="block"
                  fill="outline"
                  @click="handleExitGame"
                  color="danger"
                >
                  <ion-icon :icon="exitOutline" slot="start"></ion-icon>
                  Keluar Permainan
                </ion-button>
              </div>
            </div>
          </ion-card-content>
        </ion-card>

        <!-- Leaderboard -->
        <LeaderboardCard
          :leaderboard="leaderboard"
          :current-player-id="currentPlayer.id"
          :initial-expanded="true"
        />

        <!-- Game Board Display -->
        <GameBoard
          v-if="showBoard"
          :players="gameState.players"
          :key="gameState.currentTurn + '-' + gameState.players.map(p => p.position).join('-')"
        />
      </div>

      <!-- Game Finished Section -->
      <GameFinishedSection
        v-else-if="gameState.gameFinished"
        :winner-name="getWinnerName()"
        :leaderboard="leaderboard"
        @reset-game="handleResetGame"
      />
    </ion-content>



    <!-- Settings Modal -->
    <SettingsModal
      :is-open="isSettingsOpen"
      :gemini-initialized="geminiInitialized"
      @close="closeSettings"
      @update:geminiInitialized="geminiInitialized = $event"
    />

    <!-- Tutorial Modal -->
    <TutorialModal
      :is-open="isTutorialOpen"
      @close="closeTutorial"
      @start="startGameAfterTutorial"
    />

    <!-- Game Menu Modal -->
    <GameMenuModal
      :is-open="isGameMenuOpen"
      @close="closeGameMenu"
      @exit-game="handleExitGame"
      @reset-game="handleResetGame"
    />
  </ion-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonCard,
  IonCardContent,
  IonButton,
  IonButtons,
  IonIcon,
  IonNote
} from '@ionic/vue';
import {
  settings,
  checkmark,
  informationCircle,
  ellipsisVertical,
  exitOutline
} from 'ionicons/icons';

import TutorialModal from '../components/TutorialModal.vue';
import GameSetup from '../components/GameSetup.vue';
import SettingsModal from '../components/SettingsModal.vue';
import VoiceDiceInput from '../components/VoiceDiceInput.vue';
import GameBoard from '../components/GameBoard.vue';
import CurrentPlayerCard from '../components/CurrentPlayerCard.vue';
import SubtitleCard from '../components/SubtitleCard.vue';
import DiceResultDisplay from '../components/DiceResultDisplay.vue';
import CardChallengeSection from '../components/CardChallengeSection.vue';
import LeaderboardCard from '../components/LeaderboardCard.vue';
import GameFinishedSection from '../components/GameFinishedSection.vue';
import GameMenuModal from '../components/GameMenuModal.vue';
import { useGameFlow } from '../composables/useGameFlow';
import { useVoiceInput } from '../composables/useVoiceInput';
import { useSpeech } from '../composables/useSpeech';
import { geminiService } from '../services/geminiService';
import { settingsService } from '../services/settingsService';
import { gameStateService } from '../services/gameState';
import { cardService } from '../services/cardService';

// Import data

// Use composables
const gameFlow = useGameFlow();
const voiceInput = useVoiceInput();
const speech = useSpeech();

// Destructure composable properties
const {
  gameState,
  playerNames,
  newPlayerName,
  isProcessing,
  currentPlayer,
  leaderboard,
  canProceedToNextTurn,
  loadGameState,
  initializeGameDirectly,
  nextTurn,
  exitGame,
  resetGame,
  movePlayerAndCheckSquare,
  getWinnerName,
  completeCardAndNextTurn
} = gameFlow;

const {
  isListening,
  voiceTranscript,
  isListeningForDice,
  diceVoiceTranscript,
  stopVoiceDiceInput,
  startVoiceInput,
  stopVoiceInput
} = voiceInput;

const {
  subtitlesEnabled,
  currentSubtitle,
  isShowingSubtitle
} = speech;

// Local reactive state
const geminiInitialized = ref(false);
const currentCard = computed(() => gameState.value.currentCard);
const isSettingsOpen = ref(false);
const isGameMenuOpen = ref(false);
const isTutorialOpen = ref(false);
const showBoard = computed(() => settingsService.getShowBoard());

// Additional computed properties
const showQRScanner = computed((): boolean => {
  const position = currentPlayer.value.position;
  const squareType = getBoardSquareType(position);
  const isOnCardSquare = ['HOAX', 'HURUF ACAK', 'TEBAK KATA', 'MERANGKAI CERITA', 'KOLABORASI BERDAMPAK'].includes(squareType);
  const hasNoCurrentCard = !gameState.value.currentCard;
  const hasDiceRoll = gameState.value.lastDiceRoll !== null;

  return isOnCardSquare && hasNoCurrentCard && hasDiceRoll;
});

// Helper functions
const getBoardSquareType = (position: number): string => {
  return cardService.getBoardSquareInfo(position).type;
};

// Modal functions
const openSettings = (): void => {
  isSettingsOpen.value = true;
};

const closeSettings = (): void => {
  isSettingsOpen.value = false;
};

// Game menu functions
const openGameMenu = (): void => {
  isGameMenuOpen.value = true;
};

const closeGameMenu = (): void => {
  isGameMenuOpen.value = false;
};

const handleExitGame = async (): Promise<void> => {
  closeGameMenu();
  await exitGame();
};

const handleResetGame = async (): Promise<void> => {
  closeGameMenu();
  await resetGame();
};



// Tutorial functions
const startGame = async (): Promise<void> => {
  // Check if tutorial should be shown
  if (settingsService.getShowTutorial()) {
    isTutorialOpen.value = true;
    return;
  }

  // Start game directly if tutorial is disabled
  await initializeGameDirectly();
};

const closeTutorial = (): void => {
  isTutorialOpen.value = false;
};

const startGameAfterTutorial = async (dontShowAgain: boolean): Promise<void> => {
  // Save tutorial preference
  if (dontShowAgain) {
    settingsService.setShowTutorial(false);
  }

  // Close tutorial and start game
  isTutorialOpen.value = false;
  await initializeGameDirectly();
};

// Dice input functions
const handleManualDiceInput = async (diceNumber: number): Promise<void> => {
  try {
    // Set the dice roll directly for manual input
    await gameStateService.setDiceRoll(diceNumber);

    // Update local game state
    gameState.value = gameStateService.getGameState();

    // // Announce result if speech is available
    // if (geminiService.isSpeechAvailable()) {
    //   await geminiService.speak(`${currentPlayer.value.name} memilih angka ${diceNumber}!`);
    // }

    // Automatically move player and check square for manual input
    await movePlayerAndCheckSquare();
  } catch (error) {
    console.error('Manual dice input error:', error);
  }
};

const handleStartVoiceDiceInput = async (): Promise<void> => {
  // Call the voice input function with the movePlayerAndCheckSquare callback
  await voiceInput.startVoiceDiceInput(movePlayerAndCheckSquare);
};

// Card functions
const startCardVoiceInput = async (): Promise<void> => {
  await startVoiceInput(currentCard.value?.cardType);
};

const handleTimerFinish = (): void => {
  console.log('Timer finished');
};

const playWarningSound = (): void => {
  console.log('Warning sound');
};

const playDangerSound = (): void => {
  console.log('Danger sound');
};

onMounted(async () => {
  await loadGameState();

  // Load API key from settings and initialize Gemini
  const savedApiKey = settingsService.getGeminiApiKey();
  if (savedApiKey) {
    geminiService.initialize(savedApiKey);
    geminiInitialized.value = true;
  }

  // Welcome message in Indonesian after a short delay to ensure voice is loaded
  setTimeout(async () => {
    if (geminiService.isSpeechAvailable()) {
      try {
        await geminiService.speak('Selamat datang di LiterAksi! Aplikasi siap digunakan.');
      } catch (error) {
        console.log('Welcome speech failed:', error);
      }
    }
  }, 2000);
});
</script>

<style scoped>
/* Ensure proper header spacing */
ion-content {
  --padding-top: 0;
}

.game-section {
  padding: 16px;
}

.game-controls-card {
  margin-bottom: 16px;
}

.control-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--ion-color-light-shade);
  text-align: center;
}

.next-turn-section {
  text-align: center;
}

.exit-game-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--ion-color-light-shade);
}

.exit-game-section ion-button {
  --border-width: 1px;
  --border-style: dashed;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* ===== ENHANCED PAGE STYLING ===== */

/* Header Enhancement */
ion-header ion-toolbar {
  --background: var(--bg-gradient-primary);
  --color: white;
  --border-width: 0;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

ion-header ion-title {
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Page Background Enhancement */
ion-content {
  --background: linear-gradient(180deg, #f8f9ff 0%, #e8f4fd 100%);
}

/* Card Enhancements */
ion-card {
  --background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(74, 144, 226, 0.1);
  margin: 16px;
  overflow: hidden;
}

/* Chip Enhancements */
ion-chip {
  --background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  --color: white;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

ion-chip[color="success"] {
  --background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
}

ion-chip[color="warning"] {
  --background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
  --color: #333;
}

/* Fun Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating-element {
  animation: float 3s ease-in-out infinite;
}

/* Status Message Enhancement */
.status-message {
  background: var(--bg-gradient-success);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  text-align: center;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 16px;
}

/* Toast/Alert Enhancements */
.alert-wrapper {
  --background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Loading spinner enhancement */
ion-spinner {
  --color: var(--literaksi-primary);
}
</style>