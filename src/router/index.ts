import { createRouter, createWebHistory } from '@ionic/vue-router';
import { RouteRecordRaw } from 'vue-router';
import HomePage from '../views/HomePage.vue'
import InstructorPage from '../views/InstructorPage.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/instructor'
  },
  {
    path: '/home',
    name: 'Home',
    component: HomePage
  },
  {
    path: '/instructor',
    name: 'Instructor',
    component: InstructorPage
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router
