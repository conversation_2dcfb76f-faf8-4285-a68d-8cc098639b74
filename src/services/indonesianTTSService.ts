export interface IndonesianTTSOptions {
  rate?: number;
  pitch?: number;
  volume?: number;
  voice?: string;
}

export type TTSProviderType = 'ResponsiveVoice' | 'SpeechSynthesis' | 'Auto';

export interface TTSSettings {
  preferredProvider: TTSProviderType;
  autoFallback: boolean;
}

export interface TTSEvent {
  type: 'start' | 'end' | 'error';
  text?: string;
  error?: string;
}

export interface IndonesianTTSProvider {
  name: string;
  isAvailable: () => boolean;
  speak: (text: string, options?: IndonesianTTSOptions) => Promise<void>;
  stop: () => void;
  getVoices: () => string[];
}

class IndonesianTTSService {
  private providers: IndonesianTTSProvider[] = [];
  private currentProvider: IndonesianTTSProvider | null = null;
  private isResponsiveVoiceLoaded = false;
  private settings: TTSSettings = {
    preferredProvider: 'Auto',
    autoFallback: true
  };
  private eventListeners: ((event: TTSEvent) => void)[] = [];

  constructor() {
    this.initializeProviders();
    this.loadResponsiveVoice();
    this.loadSettings();
  }

  private initializeProviders(): void {
    // Add ResponsiveVoice provider
    this.providers.push({
      name: 'ResponsiveVoice',
      isAvailable: () => this.isResponsiveVoiceAvailable(),
      speak: (text: string, options?: IndonesianTTSOptions) => this.speakWithResponsiveVoice(text, options),
      stop: () => this.stopResponsiveVoice(),
      getVoices: () => ['Indonesian Male', 'Indonesian Female']
    });

    // Add Web Audio API provider with Indonesian configuration
    this.providers.push({
      name: 'WebAudioAPI',
      isAvailable: () => this.isWebAudioAvailable(),
      speak: (text: string, options?: IndonesianTTSOptions) => this.speakWithWebAudio(text, options),
      stop: () => this.stopWebAudio(),
      getVoices: () => ['Browser Indonesian']
    });

    // Add SpeechSynthesis with forced Indonesian settings
    this.providers.push({
      name: 'SpeechSynthesis',
      isAvailable: () => 'speechSynthesis' in window,
      speak: (text: string, options?: IndonesianTTSOptions) => this.speakWithSpeechSynthesis(text, options),
      stop: () => this.stopSpeechSynthesis(),
      getVoices: () => ['System Default']
    });
  }

  private loadResponsiveVoice(): void {
    // Load ResponsiveVoice script dynamically (paid version)
    if (typeof window !== 'undefined' && !window.responsiveVoice) {
      const script = document.createElement('script');
      script.src = 'https://code.responsivevoice.org/responsivevoice.js?key=jQZ2zcdq';
      script.onload = () => {
        this.isResponsiveVoiceLoaded = true;
        console.log('ResponsiveVoice loaded successfully');
        
        // Wait a bit for ResponsiveVoice to fully initialize
        setTimeout(() => {
          if (window.responsiveVoice && window.responsiveVoice.voiceSupport()) {
            console.log('ResponsiveVoice is ready with Indonesian support');
          }
        }, 1000);
      };
      script.onerror = () => {
        console.warn('Failed to load ResponsiveVoice');
      };
      document.head.appendChild(script);
    }
  }

  private isResponsiveVoiceAvailable(): boolean {
    return this.isResponsiveVoiceLoaded && 
           typeof window !== 'undefined' && 
           window.responsiveVoice && 
           window.responsiveVoice.voiceSupport();
  }

  private async speakWithResponsiveVoice(text: string, options?: IndonesianTTSOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!window.responsiveVoice) {
        reject(new Error('ResponsiveVoice not available'));
        return;
      }

      try {
        // Use Indonesian Male voice by default
        const voiceName = options?.voice || 'Indonesian Male';
        const settings = {
          rate: options?.rate || 0.8,
          pitch: options?.pitch || 1,
          volume: options?.volume || 1,
          onend: () => resolve(),
          onerror: (error: any) => reject(new Error(`ResponsiveVoice error: ${error}`))
        };

        window.responsiveVoice.speak(text, voiceName, settings);
      } catch (error) {
        reject(error);
      }
    });
  }

  private stopResponsiveVoice(): void {
    if (window.responsiveVoice) {
      window.responsiveVoice.cancel();
    }
  }

  private isWebAudioAvailable(): boolean {
    return typeof window !== 'undefined' && 
           'AudioContext' in window && 
           'fetch' in window;
  }

  private async speakWithWebAudio(text: string, options?: IndonesianTTSOptions): Promise<void> {
    // This is a placeholder for Web Audio API implementation
    // You could integrate with services like Google Cloud TTS, Azure TTS, or other APIs
    return new Promise((resolve, reject) => {
      // For now, we'll fall back to the next provider
      reject(new Error('Web Audio API TTS not implemented yet'));
    });
  }

  private stopWebAudio(): void {
    // Stop Web Audio API speech
  }

  private async speakWithSpeechSynthesis(text: string, options?: IndonesianTTSOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('Speech Synthesis not supported'));
        return;
      }

      const utterance = new SpeechSynthesisUtterance(text);
      
      // Force Indonesian language even without Indonesian voices
      utterance.lang = 'id-ID';
      utterance.rate = options?.rate || 0.7;
      utterance.pitch = options?.pitch || 1.0;
      utterance.volume = options?.volume || 1.0;

      // Try to find any available voice
      const voices = speechSynthesis.getVoices();
      if (voices.length > 0) {
        // Prefer any voice that might support Indonesian, fallback to first available
        utterance.voice = voices.find(voice => 
          voice.lang.startsWith('id') || 
          voice.lang.includes('indonesia') ||
          voice.name.toLowerCase().includes('indonesia')
        ) || voices[0];
      }

      utterance.onend = () => resolve();
      utterance.onerror = (event) => reject(new Error(`Speech synthesis error: ${event.error}`));

      speechSynthesis.speak(utterance);
    });
  }

  private stopSpeechSynthesis(): void {
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel();
    }
  }

  private loadSettings(): void {
    // Load settings from localStorage
    try {
      const savedSettings = localStorage.getItem('literaksi-tts-settings');
      if (savedSettings) {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
        console.log('TTS settings loaded:', this.settings);
      }
    } catch (error) {
      console.warn('Failed to load TTS settings:', error);
    }
  }

  private saveSettings(): void {
    // Save settings to localStorage
    try {
      localStorage.setItem('literaksi-tts-settings', JSON.stringify(this.settings));
      console.log('TTS settings saved:', this.settings);
    } catch (error) {
      console.warn('Failed to save TTS settings:', error);
    }
  }

  private selectBestProvider(): IndonesianTTSProvider | null {
    // If user has a preferred provider, try that first
    if (this.settings.preferredProvider !== 'Auto') {
      const preferredProvider = this.providers.find(p => p.name === this.settings.preferredProvider);
      if (preferredProvider && preferredProvider.isAvailable()) {
        console.log(`Selected preferred TTS provider: ${preferredProvider.name}`);
        return preferredProvider;
      } else if (!this.settings.autoFallback) {
        console.warn(`Preferred provider ${this.settings.preferredProvider} not available and fallback disabled`);
        return null;
      }
    }

    // Auto mode or fallback: select best available provider
    for (const provider of this.providers) {
      if (provider.isAvailable()) {
        console.log(`Selected TTS provider: ${provider.name}`);
        return provider;
      }
    }
    return null;
  }

  async speak(text: string, options?: IndonesianTTSOptions): Promise<void> {
    if (!text || text.trim().length === 0) {
      return;
    }

    // Emit start event
    this.emitEvent({ type: 'start', text });

    // Always re-select provider to ensure user preferences are respected
    // This ensures instant updates when provider is changed
    const bestProvider = this.selectBestProvider();

    // Only keep current provider if it matches the best provider selection
    if (!this.currentProvider ||
        !this.currentProvider.isAvailable() ||
        this.currentProvider !== bestProvider) {
      this.currentProvider = bestProvider;
    }

    if (!this.currentProvider) {
      const error = 'No Indonesian TTS provider available';
      this.emitEvent({ type: 'error', text, error });
      throw new Error(error);
    }

    try {
      console.log(`Speaking with ${this.currentProvider.name}:`, text);
      await this.currentProvider.speak(text, options);
      this.emitEvent({ type: 'end', text });
    } catch (error) {
      console.warn(`TTS provider ${this.currentProvider.name} failed:`, error);

      // Try next available provider
      const providerIndex = this.providers.indexOf(this.currentProvider);
      for (let i = providerIndex + 1; i < this.providers.length; i++) {
        const fallbackProvider = this.providers[i];
        if (fallbackProvider.isAvailable()) {
          console.log(`Falling back to ${fallbackProvider.name}`);
          this.currentProvider = fallbackProvider;
          try {
            await fallbackProvider.speak(text, options);
            this.emitEvent({ type: 'end', text });
            return;
          } catch (fallbackError) {
            console.warn(`Fallback provider ${fallbackProvider.name} also failed:`, fallbackError);
          }
        }
      }

      const errorMsg = 'All Indonesian TTS providers failed';
      this.emitEvent({ type: 'error', text, error: errorMsg });
      throw new Error(errorMsg);
    }
  }

  stop(): void {
    if (this.currentProvider) {
      this.currentProvider.stop();
    }
  }

  isAvailable(): boolean {
    return this.providers.some(provider => provider.isAvailable());
  }

  getCurrentProvider(): string | null {
    return this.currentProvider?.name || null;
  }

  getCurrentProviderInfo(): { name: string; preferred: string; isPreferred: boolean } | null {
    if (!this.currentProvider) {
      return null;
    }

    return {
      name: this.currentProvider.name,
      preferred: this.settings.preferredProvider,
      isPreferred: this.settings.preferredProvider === 'Auto' ||
                   this.currentProvider.name === this.settings.preferredProvider
    };
  }

  getAvailableVoices(): string[] {
    if (this.currentProvider) {
      return this.currentProvider.getVoices();
    }
    return [];
  }

  // Settings management methods
  setTTSProvider(provider: TTSProviderType): void {
    const oldProvider = this.settings.preferredProvider;
    this.settings.preferredProvider = provider;
    this.saveSettings();

    // Force immediate re-selection of provider
    this.currentProvider = null;

    // Pre-select the new provider to ensure it's ready
    const newProvider = this.selectBestProvider();
    if (newProvider) {
      this.currentProvider = newProvider;
      console.log(`TTS provider changed from ${oldProvider} to ${provider} (using ${newProvider.name})`);
    } else {
      console.warn(`TTS provider set to ${provider} but no suitable provider found`);
    }
  }

  setAutoFallback(enabled: boolean): void {
    this.settings.autoFallback = enabled;
    this.saveSettings();
    console.log(`TTS auto fallback ${enabled ? 'enabled' : 'disabled'}`);
  }

  getTTSSettings(): TTSSettings {
    return { ...this.settings };
  }

  getAvailableProviders(): { name: TTSProviderType; available: boolean; description: string }[] {
    return [
      {
        name: 'Auto',
        available: true,
        description: 'Pilih provider terbaik secara otomatis'
      },
      {
        name: 'ResponsiveVoice',
        available: true,
        description: 'Suara Indonesia berkualitas tinggi (online)'
      },
      {
        name: 'SpeechSynthesis',
        available: true,
        description: 'Browser default (offline, kualitas bervariasi)'
      }
    ];
  }

  // Event listener methods
  addEventListener(listener: (event: TTSEvent) => void): void {
    this.eventListeners.push(listener);
  }

  removeEventListener(listener: (event: TTSEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  private emitEvent(event: TTSEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in TTS event listener:', error);
      }
    });
  }

  // Test method to verify Indonesian TTS is working
  async testIndonesianSpeech(): Promise<boolean> {
    try {
      await this.speak('Tes suara Indonesia berhasil!');
      return true;
    } catch (error) {
      console.error('Indonesian TTS test failed:', error);
      return false;
    }
  }
}

// Extend window interface for ResponsiveVoice
declare global {
  interface Window {
    responsiveVoice: any;
  }
}

export const indonesianTTSService = new IndonesianTTSService(); 