export interface VoiceInputOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
}

export interface VoiceInputResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

class VoiceInputService {
  private recognition: any = null;
  private isListening = false;
  private onResult: ((result: VoiceInputResult) => void) | null = null;
  private onError: ((error: string) => void) | null = null;
  private onEnd: (() => void) | null = null;

  constructor() {
    this.initializeRecognition();
  }

  private initializeRecognition(): void {
    // Check for speech recognition support
    const SpeechRecognition = (window as any).SpeechRecognition || 
                             (window as any).webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      this.recognition = new SpeechRecognition();
      this.setupRecognitionEvents();
    }
  }

  private setupRecognitionEvents(): void {
    if (!this.recognition) return;

    this.recognition.onresult = (event: any) => {
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcript = result[0].transcript;
        const confidence = result[0].confidence;
        const isFinal = result.isFinal;

        if (this.onResult) {
          this.onResult({
            transcript: transcript.trim(),
            confidence: confidence || 0.8,
            isFinal
          });
        }
      }
    };

    this.recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error);
      this.isListening = false;
      
      let errorMessage = 'Terjadi kesalahan saat mendengarkan suara.';
      switch (event.error) {
        case 'no-speech':
          errorMessage = 'Tidak ada suara yang terdeteksi. Silakan coba lagi.';
          break;
        case 'audio-capture':
          errorMessage = 'Mikrofon tidak dapat diakses. Periksa pengaturan mikrofon.';
          break;
        case 'not-allowed':
          errorMessage = 'Akses mikrofon ditolak. Izinkan akses mikrofon di pengaturan browser.';
          break;
        case 'network':
          errorMessage = 'Terjadi kesalahan jaringan. Periksa koneksi internet.';
          break;
      }

      if (this.onError) {
        this.onError(errorMessage);
      }
    };

    this.recognition.onend = () => {
      this.isListening = false;
      if (this.onEnd) {
        this.onEnd();
      }
    };

    this.recognition.onstart = () => {
      this.isListening = true;
    };
  }

  startListening(
    options: VoiceInputOptions = {},
    onResult: (result: VoiceInputResult) => void,
    onError?: (error: string) => void,
    onEnd?: () => void
  ): boolean {
    if (!this.recognition) {
      const error = 'Pengenalan suara tidak didukung di browser ini.';
      if (onError) onError(error);
      return false;
    }

    if (this.isListening) {
      this.stopListening();
    }

    // Set callbacks
    this.onResult = onResult;
    this.onError = onError || null;
    this.onEnd = onEnd || null;

    // Configure recognition for Indonesian
    this.recognition.lang = options.language || 'id-ID'; // Indonesian by default
    this.recognition.continuous = options.continuous || false;
    this.recognition.interimResults = options.interimResults || true;
    this.recognition.maxAlternatives = options.maxAlternatives || 3; // More alternatives for better Indonesian recognition
    this.recognition.grammars = null; // Allow free-form speech

    try {
      this.recognition.start();
      return true;
    } catch (error) {
      const errorMsg = 'Gagal memulai pengenalan suara.';
      if (this.onError) this.onError(errorMsg);
      return false;
    }
  }

  stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
    }
  }

  isSupported(): boolean {
    return this.recognition !== null;
  }

  isCurrentlyListening(): boolean {
    return this.isListening;
  }

  // Specific methods for different card types
  async listenForHoaxFaktaAnswer(): Promise<string> {
    return new Promise((resolve, reject) => {
      this.startListening(
        { language: 'id-ID', continuous: false },
        (result) => {
          if (result.isFinal) {
            const answer = this.parseHoaxFaktaAnswer(result.transcript);
            resolve(answer);
          }
        },
        (error) => reject(new Error(error)),
        () => {
          // If no final result, resolve with empty string
          resolve('');
        }
      );
    });
  }

  async listenForWordAnswer(): Promise<string> {
    return new Promise((resolve, reject) => {
      this.startListening(
        { language: 'id-ID', continuous: false },
        (result) => {
          if (result.isFinal) {
            resolve(result.transcript.toUpperCase().trim());
          }
        },
        (error) => reject(new Error(error)),
        () => resolve('')
      );
    });
  }

  async listenForStory(): Promise<string> {
    return new Promise((resolve, reject) => {
      let fullStory = '';
      
      this.startListening(
        { language: 'id-ID', continuous: true, interimResults: true },
        (result) => {
          if (result.isFinal) {
            fullStory += result.transcript + ' ';
          }
        },
        (error) => reject(new Error(error)),
        () => resolve(fullStory.trim())
      );
    });
  }

  private parseHoaxFaktaAnswer(transcript: string): string {
    const lowerTranscript = transcript.toLowerCase().trim();
    
    // Enhanced Indonesian keywords for "hoax"
    const hoaxKeywords = [
      'hoax', 'hoaks', 'salah', 'bohong', 'palsu', 'tidak benar', 
      'keliru', 'menyesatkan', 'fake', 'fitnah', 'dusta',
      'bukan fakta', 'tidak akurat', 'manipulasi'
    ];
    
    // Enhanced Indonesian keywords for "fakta"
    const faktaKeywords = [
      'fakta', 'benar', 'betul', 'facts', 'iya', 'ya', 
      'memang benar', 'sesuai kenyataan', 'akurat', 'valid',
      'sahih', 'nyata', 'riil', 'asli', 'sungguh'
    ];
    
    // Check for hoax keywords first (more specific)
    for (const keyword of hoaxKeywords) {
      if (lowerTranscript.includes(keyword)) {
        return 'hoax';
      }
    }
    
    // Then check for fakta keywords
    for (const keyword of faktaKeywords) {
      if (lowerTranscript.includes(keyword)) {
        return 'fakta';
      }
    }
    
    // If no clear match, return the original transcript
    return transcript;
  }

  // Method for voice dice input (Indonesian numbers 1-6)
  async listenForDiceNumber(): Promise<number> {
    return new Promise((resolve, reject) => {
      this.startListening(
        { language: 'id-ID', continuous: false },
        (result) => {
          if (result.isFinal) {
            const number = this.parseIndonesianNumber(result.transcript);
            resolve(number);
          }
        },
        (error) => reject(new Error(error)),
        () => {
          // If no final result, resolve with 0 (invalid)
          resolve(0);
        }
      );
    });
  }

  private parseIndonesianNumber(transcript: string): number {
    const lowerTranscript = transcript.toLowerCase().trim();

    // Indonesian number words for dice (1-6)
    const numberMap: { [key: string]: number } = {
      'satu': 1,
      'dua': 2,
      'tiga': 3,
      'empat': 4,
      'lima': 5,
      'enam': 6,
      '1': 1,
      '2': 2,
      '3': 3,
      '4': 4,
      '5': 5,
      '6': 6,
      'se': 1, // Alternative for "satu"
      'atu': 1, // Partial recognition
      'ua': 2, // Partial recognition
      'iga': 3, // Partial recognition
      'mpat': 4, // Partial recognition
      'ima': 5, // Partial recognition
      'nam': 6 // Partial recognition
    };

    // Check for exact matches first
    for (const [word, number] of Object.entries(numberMap)) {
      if (lowerTranscript.includes(word)) {
        return number;
      }
    }

    // If no match found, return 0 (invalid)
    return 0;
  }

  // Method to test microphone access
  async testMicrophone(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Microphone test failed:', error);
      return false;
    }
  }
}

export const voiceInputService = new VoiceInputService(); 