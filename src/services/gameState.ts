import { Preferences } from '@capacitor/preferences';

export interface Player {
  id: string;
  name: string;
  position: number;
  score: number;
  color: string;
}

export interface GameState {
  gameId: string;
  players: Player[];
  currentPlayerIndex: number;
  gameStarted: boolean;
  gameFinished: boolean;
  winner: string | null;
  currentTurn: number;
  lastDiceRoll: number | null;
  currentCard: any | null;
  timerStartTime: number | null;
  timerDuration: number | null;
}

class GameStateService {
  private gameState: GameState = {
    gameId: '',
    players: [],
    currentPlayerIndex: 0,
    gameStarted: false,
    gameFinished: false,
    winner: null,
    currentTurn: 1,
    lastDiceRoll: null,
    currentCard: null,
    timerStartTime: null,
    timerDuration: null
  };

  private readonly STORAGE_KEY = 'literaksi_game_state';

  async initializeGame(playerNames: string[]): Promise<void> {
    const colors = ['primary', 'secondary', 'tertiary', 'success', 'warning', 'danger', 'medium', 'dark'];
    
    this.gameState = {
      gameId: Date.now().toString(),
      players: playerNames.map((name, index) => ({
        id: `player_${index + 1}`,
        name,
        position: 0, // Start position
        score: 0,
        color: colors[index % colors.length]
      })),
      currentPlayerIndex: 0,
      gameStarted: true,
      gameFinished: false,
      winner: null,
      currentTurn: 1,
      lastDiceRoll: null,
      currentCard: null,
      timerStartTime: null,
      timerDuration: null
    };

    await this.saveGameState();
  }

  async loadGameState(): Promise<GameState> {
    try {
      const { value } = await Preferences.get({ key: this.STORAGE_KEY });
      if (value) {
        this.gameState = JSON.parse(value);
      }
    } catch (error) {
      console.error('Error loading game state:', error);
    }
    return this.gameState;
  }

  async saveGameState(): Promise<void> {
    try {
      await Preferences.set({
        key: this.STORAGE_KEY,
        value: JSON.stringify(this.gameState)
      });
    } catch (error) {
      console.error('Error saving game state:', error);
    }
  }

  getCurrentPlayer(): Player {
    return this.gameState.players[this.gameState.currentPlayerIndex];
  }

  async movePlayer(playerId: string, steps: number): Promise<void> {
    const player = this.gameState.players.find(p => p.id === playerId);
    if (player) {
      player.position += steps;
      // Check if player reached finish (position 80 based on 9x10 board - 1)
      if (player.position >= 89) {
        player.position = 89; // Finish position
        if (!this.gameState.gameFinished) {
          this.gameState.gameFinished = true;
          this.gameState.winner = player.id;
          player.score += 10; // Finish bonus
        }
      }
      await this.saveGameState();
    }
  }

  async movePlayerToPosition(playerId: string, position: number): Promise<void> {
    const player = this.gameState.players.find(p => p.id === playerId);
    if (player) {
      player.position = Math.max(0, Math.min(89, position)); // Ensure position is within bounds
      // Check if player reached finish
      if (player.position >= 89) {
        player.position = 89; // Finish position
        if (!this.gameState.gameFinished) {
          this.gameState.gameFinished = true;
          this.gameState.winner = player.id;
          player.score += 10; // Finish bonus
        }
      }
      await this.saveGameState();
    }
  }

  async addScore(playerId: string, points: number): Promise<void> {
    const player = this.gameState.players.find(p => p.id === playerId);
    if (player) {
      player.score += points;
      await this.saveGameState();
    }
  }

  async nextTurn(): Promise<void> {
    this.gameState.currentPlayerIndex = (this.gameState.currentPlayerIndex + 1) % this.gameState.players.length;
    this.gameState.currentTurn++;
    this.gameState.lastDiceRoll = null;
    this.gameState.currentCard = null;
    this.gameState.timerStartTime = null;
    this.gameState.timerDuration = null;
    await this.saveGameState();
  }

  async rollDice(): Promise<number> {
    const roll = Math.floor(Math.random() * 6) + 1;
    this.gameState.lastDiceRoll = roll;
    await this.saveGameState();
    return roll;
  }

  async setDiceRoll(roll: number): Promise<void> {
    if (roll >= 1 && roll <= 6) {
      this.gameState.lastDiceRoll = roll;
      await this.saveGameState();
    }
  }

  async setCurrentCard(card: any): Promise<void> {
    this.gameState.currentCard = card;
    await this.saveGameState();
  }

  async startTimer(duration: number): Promise<void> {
    this.gameState.timerStartTime = Date.now();
    this.gameState.timerDuration = duration;
    await this.saveGameState();
  }

  getRemainingTime(): number {
    if (!this.gameState.timerStartTime || !this.gameState.timerDuration) {
      return 0;
    }
    const elapsed = Date.now() - this.gameState.timerStartTime;
    const remaining = this.gameState.timerDuration - elapsed;
    return Math.max(0, remaining);
  }

  async clearTimer(): Promise<void> {
    this.gameState.timerStartTime = null;
    this.gameState.timerDuration = null;
    await this.saveGameState();
  }

  getLeaderboard(): Player[] {
    return [...this.gameState.players].sort((a, b) => b.score - a.score);
  }

  async resetGame(): Promise<void> {
    try {
      await Preferences.remove({ key: this.STORAGE_KEY });
      this.gameState = {
        gameId: '',
        players: [],
        currentPlayerIndex: 0,
        gameStarted: false,
        gameFinished: false,
        winner: null,
        currentTurn: 1,
        lastDiceRoll: null,
        currentCard: null,
        timerStartTime: null,
        timerDuration: null
      };
    } catch (error) {
      console.error('Error resetting game:', error);
    }
  }

  getGameState(): GameState {
    return { ...this.gameState };
  }

  // Convert board position to board coordinates
  getPositionCoordinates(position: number): { row: number, col: number } {
    if (position < 0 || position > 89) {
      return { row: 0, col: 0 };
    }
    
    const row = Math.floor(position / 10);
    const col = position % 10;
    return { row, col };
  }

  // Get board square type at position
  getBoardSquareType(position: number): string {
    const coords = this.getPositionCoordinates(position);
    // This would reference the actual board data
    // For now, return a placeholder
    return 'KOSONG';
  }
}

export const gameStateService = new GameStateService(); 