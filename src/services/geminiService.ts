import { GoogleGenerativeAI } from '@google/generative-ai';
import { indonesianTTSService } from './indonesianTTSService';

export interface GeminiConfig {
  apiKey: string;
}

class GeminiService {
  private genAI: GoogleGenerativeAI | null = null;
  private model: any = null;
  private speechSynthesis: SpeechSynthesis | null = null;
  private currentVoice: SpeechSynthesisVoice | null = null;

  constructor() {
    // Initialize speech synthesis with Indonesian priority
    if ('speechSynthesis' in window) {
      this.speechSynthesis = window.speechSynthesis;
      this.initializeVoice();
      
      // Ensure Indonesian voice is selected on startup
      setTimeout(() => {
        this.reloadIndonesianVoice();
      }, 1000);
    }
  }

  initialize(apiKey: string): void {
    try {
      this.genAI = new GoogleGenerativeAI(apiKey);
      this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
    } catch (error) {
      console.error('Error initializing Gemini AI:', error);
    }
  }

  private initializeVoice(): void {
    if (!this.speechSynthesis) return;

    const setVoice = () => {
      const voices = this.speechSynthesis!.getVoices();
      
      // Prioritize Indonesian voices
      this.currentVoice = voices.find(voice => 
        voice.lang === 'id-ID' || voice.lang === 'id_ID'
      ) || voices.find(voice => 
        voice.lang.startsWith('id') || voice.lang.toLowerCase().includes('indonesia')
      ) || voices.find(voice => 
        voice.name.toLowerCase().includes('indonesia') || voice.name.toLowerCase().includes('bahasa')
      ) || voices.find(voice => 
        voice.lang.includes('en') || voice.lang.includes('EN')
      ) || voices[0] || null;

      // Log available voices for debugging
      console.log('Available voices:', voices.map(v => ({ name: v.name, lang: v.lang })));
      if (this.currentVoice) {
        console.log('Selected voice:', { name: this.currentVoice.name, lang: this.currentVoice.lang });
      }
    };

    if (this.speechSynthesis.getVoices().length > 0) {
      setVoice();
    } else {
      this.speechSynthesis.onvoiceschanged = setVoice;
    }
  }

  async speak(text: string, options: { rate?: number, pitch?: number, volume?: number } = {}): Promise<void> {
    return;
    try {
      // Always use the Indonesian TTS service which now handles provider selection
      await indonesianTTSService.speak(text, {
        rate: options.rate || 0.8,
        pitch: options.pitch || 1.0,
        volume: options.volume || 1.0
      });
    } catch (error) {
      console.error('Indonesian TTS failed, trying browser fallback:', error);
      
      // Final fallback to basic browser speechSynthesis
      return new Promise((resolve, reject) => {
        if (!this.speechSynthesis) {
          reject(new Error('No speech synthesis available'));
          return;
        }

        // Cancel any ongoing speech
        this.speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        
        // Force Indonesian language and voice settings
        utterance.lang = 'id-ID'; // Explicitly set Indonesian language
        utterance.voice = this.currentVoice;
        
        // Optimized settings for Indonesian pronunciation
        utterance.rate = options.rate || 0.75; // Slower rate for clearer Indonesian pronunciation
        utterance.pitch = options.pitch || 1.0;
        utterance.volume = options.volume || 1.0;

        // Log what's being spoken for debugging
        console.log('Speaking in Indonesian (emergency fallback):', text);
        if (this.currentVoice) {
          console.log('Using voice:', this.currentVoice.name, this.currentVoice.lang);
        }

        utterance.onend = () => {
          console.log('Speech completed');
          resolve();
        };
        
        utterance.onerror = (event) => {
          console.error('Speech error:', event);
          reject(event);
        };

        // Small delay to ensure proper voice loading
        setTimeout(() => {
          this.speechSynthesis!.speak(utterance);
        }, 100);
      });
    }
  }

  stopSpeaking(): void {
    // Stop Indonesian TTS service first
    indonesianTTSService.stop();
    
    // Also stop browser speechSynthesis
    if (this.speechSynthesis) {
      this.speechSynthesis.cancel();
    }
  }

  async generateGameInstructions(cardType: string, cardData: any): Promise<string> {
    if (!this.model) {
      return this.getDefaultInstruction(cardType, cardData);
    }

    try {
      const prompt = this.createInstructionPrompt(cardType, cardData);
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating AI instructions:', error);
      return this.getDefaultInstruction(cardType, cardData);
    }
  }

  private createInstructionPrompt(cardType: string, cardData: any): string {
    switch (cardType) {
      case 'HOAX':
        return `Sebagai instruktur permainan LiterAksi, berikan instruksi yang jelas dan menarik untuk kartu Hoax atau Fakta. 
        Informasi: "${cardData.question}"
        Buatlah instruksi yang memotivasi pemain untuk berpikir kritis. Gunakan bahasa Indonesia yang ramah dan antusias.`;

      case 'HURUF ACAK':
        return `Sebagai instruktur permainan LiterAksi, berikan instruksi untuk kartu Huruf Acak.
        Huruf acak: "${cardData.scrambled}"
        Hint: "${cardData.hint}"
        Buat instruksi yang jelas dan membantu pemain memahami tugas. Gunakan bahasa Indonesia yang semangat.`;

      case 'TEBAK KATA':
        return `Sebagai instruktur permainan LiterAksi, berikan instruksi untuk kartu Tebak Kata.
        Kata: "${cardData.word}"
        Tipe: ${cardData.type}
        Buat instruksi yang menarik dan memotivasi. Gunakan bahasa Indonesia yang antusias.`;

      case 'MERANGKAI CERITA':
        return `Sebagai instruktur permainan LiterAksi, berikan instruksi untuk kartu Merangkai Cerita.
        Prompt: "${cardData.prompt}"
        Buat instruksi yang menginspirasi kreativitas. Gunakan bahasa Indonesia yang ramah dan memotivasi.`;

      case 'KOLABORASI BERDAMPAK':
        return `Sebagai instruktur permainan LiterAksi, berikan instruksi untuk kartu Kolaborasi Berdampak.
        Kegiatan: "${cardData.title}"
        Deskripsi: "${cardData.description}"
        Buat instruksi yang memotivasi kerja sama tim. Gunakan bahasa Indonesia yang semangat dan positif.`;

      default:
        return `Sebagai instruktur permainan LiterAksi yang ramah, berikan instruksi umum untuk melanjutkan permainan dengan semangat!`;
    }
  }

  private getDefaultInstruction(cardType: string, cardData: any): string {
    switch (cardType) {
      case 'HOAX':
        return `Selamat! Anda mendapat kartu Hoax atau Fakta. Dengarkan pernyataan berikut dengan saksama dan tentukan apakah ini HOAX atau FAKTA: "${cardData.question}". Anda memiliki 2 menit untuk menjawab. Selamat berpikir!`;

      case 'HURUF ACAK':
        return `Anda mendapat kartu Huruf Acak! Susunlah huruf-huruf berikut menjadi kata yang bermakna: "${cardData.scrambled}". Hint: ${cardData.hint}. Waktu Anda 3 menit. Semangat!`;

      case 'TEBAK KATA':
        return `Kartu Tebak Kata! Sebutkan 3 ${cardData.type} dari kata "${cardData.word}". Anda memiliki 4 menit. Mari tunjukkan kemampuan kosakata Anda!`;

      case 'MERANGKAI CERITA':
        return `Kartu Merangkai Cerita! ${cardData.prompt}. Buatlah cerita yang menarik dan kreatif. Waktu Anda 6 menit. Biarkan imajinasi Anda mengalir!`;

      case 'KOLABORASI BERDAMPAK':
        return `Kartu Kolaborasi Berdampak! Saatnya bekerja sama: ${cardData.title}. ${cardData.description}. Waktu kerja sama 8 menit. Mari berdampak positif bersama!`;

      default:
        return `Selamat bermain LiterAksi! Mari lanjutkan permainan dengan semangat dan sportivitas tinggi!`;
    }
  }

  async generateMotivationalQuote(): Promise<string> {
    if (!this.model) {
      return this.getRandomMotivationalQuote();
    }

    try {
      const prompt = `Berikan satu kutipan motivasi singkat dalam bahasa Indonesia tentang membaca, belajar, atau literasi. Kutipan harus menginspirasi dan positif.`;
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating motivational quote:', error);
      return this.getRandomMotivationalQuote();
    }
  }

  private getRandomMotivationalQuote(): string {
    const quotes = [
      "Membaca adalah jendela dunia. Semakin banyak membaca, semakin luas wawasan kita.",
      "Buku adalah teman terbaik yang tidak pernah mengkhianati.",
      "Ilmu pengetahuan dimulai dari rasa ingin tahu dan membaca.",
      "Dengan membaca, kita bisa mengunjungi ribuan tempat tanpa beranjak dari tempat duduk.",
      "Investasi terbaik adalah investasi pengetahuan melalui membaca.",
      "Literasi adalah kunci untuk membuka pintu masa depan yang cerah.",
      "Setiap halaman yang dibaca adalah langkah menuju kebijaksanaan.",
      "Membaca tidak hanya mengisi pikiran, tetapi juga memperkaya jiwa."
    ];
    return quotes[Math.floor(Math.random() * quotes.length)];
  }

  async evaluateStoryResponse(story: string): Promise<{ score: 'sempurna' | 'bagus' | 'lumayan', feedback: string }> {
    if (!this.model) {
      return this.getDefaultStoryEvaluation(story);
    }

    try {
      const prompt = `Evaluasi cerita pendek berikut berdasarkan kreativitas, struktur, dan kualitas bahasa:
      "${story}"
      
      Berikan penilaian dalam format:
      Skor: sempurna/bagus/lumayan
      Feedback: [komentar konstruktif dalam bahasa Indonesia]`;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Parse response
      const scoreMatch = text.match(/Skor:\s*(sempurna|bagus|lumayan)/i);
      const feedbackMatch = text.match(/Feedback:\s*(.+)/s);
      
      const score = (scoreMatch?.[1]?.toLowerCase() as 'sempurna' | 'bagus' | 'lumayan') || 'lumayan';
      const feedback = feedbackMatch?.[1]?.trim() || 'Cerita yang menarik! Terus berlatih untuk menjadi lebih baik.';
      
      return { score, feedback };
    } catch (error) {
      console.error('Error evaluating story:', error);
      return this.getDefaultStoryEvaluation(story);
    }
  }

  private getDefaultStoryEvaluation(story: string): { score: 'sempurna' | 'bagus' | 'lumayan', feedback: string } {
    const wordCount = story.trim().split(/\s+/).length;
    
    if (wordCount >= 50) {
      return {
        score: 'sempurna',
        feedback: 'Cerita yang sangat baik! Panjang dan detail, menunjukkan kreativitas tinggi.'
      };
    } else if (wordCount >= 25) {
      return {
        score: 'bagus', 
        feedback: 'Cerita yang bagus! Ada alur yang jelas dan cukup detail.'
      };
    } else {
      return {
        score: 'lumayan',
        feedback: 'Cerita yang baik sebagai permulaan. Coba lebih diperpanjang untuk detail yang lebih kaya.'
      };
    }
  }

  isAvailable(): boolean {
    return this.model !== null;
  }

  isSpeechAvailable(): boolean {
    // Check if Indonesian TTS service is available (handles provider selection internally)
    return indonesianTTSService.isAvailable();
  }

  // Method to get current voice information for debugging
  getCurrentVoiceInfo(): { name: string; lang: string } | null {
    const provider = indonesianTTSService.getCurrentProvider();
    const settings = indonesianTTSService.getTTSSettings();
    
    if (provider) {
      return {
        name: `${provider} (${settings.preferredProvider === 'Auto' ? 'Auto-selected' : 'User-selected'})`,
        lang: 'id-ID'
      };
    }
    
    if (this.currentVoice) {
      return {
        name: `${this.currentVoice.name} (Emergency fallback)`,
        lang: this.currentVoice.lang
      };
    }
    return null;
  }

  // Method to get all available Indonesian voices
  getIndonesianVoices(): SpeechSynthesisVoice[] {
    if (!this.speechSynthesis) return [];
    
    const voices = this.speechSynthesis.getVoices();
    return voices.filter(voice => 
      voice.lang.startsWith('id') || 
      voice.lang.toLowerCase().includes('indonesia') ||
      voice.name.toLowerCase().includes('indonesia') ||
      voice.name.toLowerCase().includes('bahasa')
    );
  }

  // Method to manually select the best Indonesian voice
  selectBestIndonesianVoice(): void {
    if (!this.speechSynthesis) return;
    
    const voices = this.speechSynthesis.getVoices();
    
    // Priority order for Indonesian voices
    const priorities = [
      'id-ID',
      'id_ID', 
      'id',
      'indonesia',
      'bahasa'
    ];
    
    for (const priority of priorities) {
      const voice = voices.find(v => 
        v.lang.toLowerCase() === priority.toLowerCase() ||
        v.lang.toLowerCase().startsWith(priority.toLowerCase()) ||
        v.name.toLowerCase().includes(priority.toLowerCase())
      );
      
      if (voice) {
        this.currentVoice = voice;
        console.log('Indonesian voice selected:', voice.name, voice.lang);
        return;
      }
    }
    
    // Fallback to any available voice
    this.currentVoice = voices[0] || null;
    console.log('Fallback voice selected:', this.currentVoice?.name, this.currentVoice?.lang);
  }

  // Method to force reload voices and re-select Indonesian
  reloadIndonesianVoice(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.speechSynthesis) {
        resolve();
        return;
      }

      const trySelectVoice = () => {
        this.selectBestIndonesianVoice();
        resolve();
      };

      // Force reload voices
      if (this.speechSynthesis.getVoices().length === 0) {
        this.speechSynthesis.onvoiceschanged = () => {
          trySelectVoice();
          this.speechSynthesis!.onvoiceschanged = null; // Remove listener
        };
      } else {
        trySelectVoice();
      }
    });
  }

  // Method to get TTS settings (delegates to Indonesian TTS service)
  getTTSSettings() {
    return indonesianTTSService.getTTSSettings();
  }

  // Method to get available TTS providers (delegates to Indonesian TTS service)
  getAvailableTTSProviders() {
    return indonesianTTSService.getAvailableProviders();
  }

  // Method to set TTS provider (delegates to Indonesian TTS service)
  setTTSProvider(provider: any) {
    indonesianTTSService.setTTSProvider(provider);
  }

  // Method to test Indonesian TTS (delegates to Indonesian TTS service)
  async testIndonesianTTS(): Promise<boolean> {
    return await indonesianTTSService.testIndonesianSpeech();
  }
}

export const geminiService = new GeminiService(); 