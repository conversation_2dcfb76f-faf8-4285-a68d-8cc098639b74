import cardsData from '../data/cards.json';
import gameConfig from '../data/gameConfig.json';

export interface Card {
  id: string;
  type: string;
  [key: string]: any;
}

export interface CardEvaluation {
  score: number;
  feedback: string;
  correct: boolean;
}

class CardService {
  private cards: { [key: string]: any[] } = cardsData;
  private config = gameConfig;

  /**
   * Get a card by its ID
   */
  getCardById(cardId: string): Card | null {
    const cardType = this.getCardTypeFromId(cardId);
    const dataKey = (this.config.cardTypes as any)[cardType];

    if (dataKey && this.cards[dataKey]) {
      const card = this.cards[dataKey].find((card: any) => card.id === cardId);
      if (card) {
        // Preserve original properties and add cardType as a separate property
        return { ...card, cardType, type: card.type || cardType };
      }
    }

    return null;
  }

  /**
   * Get all cards of a specific type
   */
  getCardsByType(type: string): Card[] {
    const dataKey = (this.config.cardTypes as any)[type];

    if (dataKey && this.cards[dataKey]) {
      return this.cards[dataKey].map(card => ({ ...card, cardType: type, type: card.type || type }));
    }

    return [];
  }

  /**
   * Get a random card of a specific type
   */
  getRandomCardByType(type: string): Card | null {
    const cards = this.getCardsByType(type);
    if (cards.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * cards.length);
    return cards[randomIndex];
  }

  /**
   * Determine card type from card ID
   */
  getCardTypeFromId(cardId: string): string {
    const prefix = cardId.substring(0, 2).toUpperCase();
    const typeMap: { [key: string]: string } = {
      'HF': 'HOAX',
      'HA': 'HURUF ACAK',
      'TK': 'TEBAK KATA',
      'MC': 'MERANGKAI CERITA',
      'KB': 'KOLABORASI BERDAMPAK'
    };
    return typeMap[prefix] || 'UNKNOWN';
  }

  /**
   * Get scoring configuration for a card type
   */
  getCardScoring(type: string): any {
    return (this.config.scoring as any)[type] || {};
  }

  /**
   * Get time limit for a card type
   */
  getTimeLimit(type: string): number {
    const scoring = this.getCardScoring(type);
    return scoring.timeLimit || 120; // default 2 minutes
  }

  /**
   * Evaluate Hoax/Fakta answer
   */
  evaluateHoaxFakta(card: any, answer: string): CardEvaluation {
    const normalizedAnswer = answer.toLowerCase().trim();
    const correctAnswer = card.answer.toLowerCase();
    const correct = normalizedAnswer === correctAnswer;
    
    return {
      score: correct ? this.config.scoring.HOAX.correct : 0,
      feedback: correct 
        ? `Benar! ${card.explanation}` 
        : `Salah. Jawaban yang benar adalah ${card.answer}. ${card.explanation}`,
      correct
    };
  }

  /**
   * Evaluate Huruf Acak answer
   */
  evaluateHurufAcak(card: any, answer: string): CardEvaluation {
    const normalizedAnswer = answer.toUpperCase().trim();
    const correctAnswer = card.answer.toUpperCase();
    const correct = normalizedAnswer === correctAnswer;
    
    return {
      score: correct ? this.config.scoring['HURUF ACAK'].correct : 0,
      feedback: correct 
        ? `Benar! Kata yang tepat adalah ${card.answer}` 
        : `Salah. Jawaban yang benar adalah ${card.answer}`,
      correct
    };
  }

  /**
   * Evaluate Tebak Kata answer
   */
  evaluateTebakKata(card: any, answers: string[]): CardEvaluation {
    const normalizedAnswers = answers.map(a => a.toLowerCase().trim());
    const correctAnswers = card.answers.map((a: string) => a.toLowerCase());
    
    const correctCount = normalizedAnswers.filter(answer => 
      correctAnswers.some((correct: string) => correct.includes(answer) || answer.includes(correct))
    ).length;
    
    const correct = correctCount >= 2; // Need at least 2 correct answers
    
    return {
      score: correct ? this.config.scoring['TEBAK KATA'].correct : 0,
      feedback: correct 
        ? `Bagus! Anda menjawab ${correctCount} dengan benar` 
        : `Kurang tepat. Contoh jawaban: ${correctAnswers.slice(0, 3).join(', ')}`,
      correct
    };
  }

  /**
   * Evaluate Merangkai Cerita (basic evaluation)
   */
  evaluateMerangkaiCerita(story: string): CardEvaluation {
    const wordCount = story.trim().split(/\s+/).length;
    const sentenceCount = story.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    
    let score: keyof typeof this.config.scoring['MERANGKAI CERITA'];
    let feedback: string;
    
    if (wordCount >= 50 && sentenceCount >= 3) {
      score = 'sempurna';
      feedback = 'Cerita yang sangat baik! Panjang, detail, dan memiliki alur yang jelas.';
    } else if (wordCount >= 25 && sentenceCount >= 2) {
      score = 'bagus';
      feedback = 'Cerita yang bagus! Ada alur yang jelas dan cukup detail.';
    } else {
      score = 'lumayan';
      feedback = 'Cerita yang baik sebagai permulaan. Coba lebih diperpanjang untuk detail yang lebih kaya.';
    }
    
    return {
      score: this.config.scoring['MERANGKAI CERITA'][score],
      feedback,
      correct: true // Story telling is always considered "correct"
    };
  }

  /**
   * Get card type display title
   */
  getCardTypeTitle(type: string): string {
    const titles: { [key: string]: string } = {
      'HOAX': 'Hoax atau Fakta',
      'HURUF ACAK': 'Huruf Acak',
      'TEBAK KATA': 'Tebak Kata',
      'MERANGKAI CERITA': 'Merangkai Cerita',
      'KOLABORASI BERDAMPAK': 'Kolaborasi Berdampak'
    };
    return titles[type] || type;
  }

  /**
   * Get card instructions based on type
   */
  getCardInstructions(card: Card): string {
    switch (card.type) {
      case 'HOAX':
        return `Tentukan apakah pernyataan berikut HOAX atau FAKTA: "${card.question}"`;
      
      case 'HURUF ACAK':
        return `Susunlah huruf-huruf "${card.scrambled}" menjadi kata yang bermakna. Hint: ${card.hint}`;
      
      case 'TEBAK KATA':
        return `Sebutkan 3 ${(card as any).type === 'sinonim' ? 'sinonim' : 'antonim'} dari kata "${card.word}"`;
      
      case 'MERANGKAI CERITA':
        return card.prompt;
      
      case 'KOLABORASI BERDAMPAK':
        return `${card.title}: ${card.description}`;
      
      default:
        return 'Ikuti instruksi pada kartu';
    }
  }

  /**
   * Validate card ID format
   */
  isValidCardId(cardId: string): boolean {
    const pattern = /^(hf|ha|tk|mc|kb)\d{3}$/i;
    return pattern.test(cardId);
  }

  /**
   * Get all available card types
   */
  getAvailableCardTypes(): string[] {
    return Object.keys(this.config.cardTypes);
  }

  /**
   * Get board square information
   */
  getBoardSquareInfo(position: number): { type: string, description: string } {
    const row = Math.floor(position / 10);
    const col = position % 10;
    
    if (row < 0 || row >= this.config.board.length || col < 0 || col >= this.config.board[row].length) {
      return { type: 'KOSONG', description: 'Petak kosong' };
    }
    
    const squareType = this.config.board[row][col];
    const description = (this.config.specialSquares as any)[squareType] || 'Petak khusus';
    
    return { type: squareType, description };
  }

  /**
   * Check if position requires a card
   */
  requiresCard(position: number): boolean {
    const { type } = this.getBoardSquareInfo(position);
    return ['HOAX', 'HURUF ACAK', 'TEBAK KATA', 'MERANGKAI CERITA', 'KOLABORASI BERDAMPAK'].includes(type);
  }

  /**
   * Get card type for board position
   */
  getCardTypeForPosition(position: number): string | null {
    const { type } = this.getBoardSquareInfo(position);
    return this.requiresCard(position) ? type : null;
  }
}

export const cardService = new CardService(); 