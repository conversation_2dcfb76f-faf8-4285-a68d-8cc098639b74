import { reactive } from 'vue';

export interface AppSettings {
  geminiApiKey: string;
  subtitlesEnabled: boolean;
  showTutorial: boolean;
  showBoard: boolean;
  diceInputMethod: 'voice' | 'manual';
}

class SettingsService {
  private settings: AppSettings = reactive({
    geminiApiKey: '',
    subtitlesEnabled: true,
    showTutorial: true,
    showBoard: true,
    diceInputMethod: 'voice'
  });

  private readonly STORAGE_KEY = 'literaksi-app-settings';

  constructor() {
    this.loadSettings();
  }

  private loadSettings(): void {
    try {
      const savedSettings = localStorage.getItem(this.STORAGE_KEY);
      if (savedSettings) {
        // Use Object.assign to preserve reactivity instead of replacing the reactive object
        Object.assign(this.settings, JSON.parse(savedSettings));
        console.log('App settings loaded');
      }
    } catch (error) {
      console.warn('Failed to load app settings:', error);
    }
  }

  private saveSettings(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
      console.log('App settings saved');
    } catch (error) {
      console.warn('Failed to save app settings:', error);
    }
  }

  // Gemini API Key methods
  setGeminiApiKey(apiKey: string): void {
    this.settings.geminiApiKey = apiKey;
    this.saveSettings();
  }

  getGeminiApiKey(): string {
    return this.settings.geminiApiKey;
  }

  hasGeminiApiKey(): boolean {
    return this.settings.geminiApiKey.length > 10;
  }

  clearGeminiApiKey(): void {
    this.settings.geminiApiKey = '';
    this.saveSettings();
  }

  // Get all settings
  getSettings(): AppSettings {
    return { ...this.settings };
  }

  // Subtitle settings methods
  setSubtitlesEnabled(enabled: boolean): void {
    this.settings.subtitlesEnabled = enabled;
    this.saveSettings();
  }

  getSubtitlesEnabled(): boolean {
    return this.settings.subtitlesEnabled;
  }

  // Tutorial settings methods
  setShowTutorial(show: boolean): void {
    this.settings.showTutorial = show;
    this.saveSettings();
  }

  getShowTutorial(): boolean {
    return this.settings.showTutorial;
  }

  // Board display settings methods
  setShowBoard(show: boolean): void {
    this.settings.showBoard = show;
    this.saveSettings();
  }

  getShowBoard(): boolean {
    return this.settings.showBoard;
  }

  // Dice input method settings methods
  setDiceInputMethod(method: 'voice' | 'manual'): void {
    this.settings.diceInputMethod = method;
    this.saveSettings();
  }

  getDiceInputMethod(): 'voice' | 'manual' {
    return this.settings.diceInputMethod;
  }

  // Reset all settings
  resetSettings(): void {
    // Use Object.assign to preserve reactivity instead of replacing the reactive object
    Object.assign(this.settings, {
      geminiApiKey: '',
      subtitlesEnabled: true,
      showTutorial: true,
      showBoard: true,
      diceInputMethod: 'voice'
    });
    this.saveSettings();
  }
}

export const settingsService = new SettingsService();
