{"hoaxFakta": [{"id": "hf001", "question": "Indonesia adalah negara dengan jumlah pulau terbanyak di dunia dengan lebih dari 17.000 pulau", "answer": "fakta", "explanation": "Indonesia memiliki 17.508 pulau yang terdaftar resmi"}, {"id": "hf002", "question": "<PERSON><PERSON>in <PERSON>VID-19 dapat mengubah DNA manusia", "answer": "hoax", "explanation": "Vaksin mRNA tidak mengubah atau berinteraksi dengan DNA manusia"}, {"id": "hf003", "question": "Air putih yang diminum setelah makan dapat mengencerkan asam lambung dan mengganggu pencernaan", "answer": "hoax", "explanation": "Air putih dalam jumlah wajar tidak mengganggu proses pencernaan"}, {"id": "hf004", "question": "Membaca dalam cahaya redup dapat merusak mata secara permanen", "answer": "hoax", "explanation": "Membaca dalam cahaya redup hanya menyebabkan kelelahan mata sementara"}, {"id": "hf005", "question": "<PERSON><PERSON> memiliki satu satelit alami yaitu <PERSON>", "answer": "fakta", "explanation": "<PERSON><PERSON><PERSON> adalah satu-satunya satelit al<PERSON>"}], "hurufAcak": [{"id": "ha001", "scrambled": "AISENNODIN", "answer": "INDONESIA", "hint": "Negara kepulauan"}, {"id": "ha002", "scrambled": "GNECAED", "answer": "CADANG", "hint": "Simpanan atau persediaan"}, {"id": "ha003", "scrambled": "AKETRAIR", "answer": "KREATIF", "hint": "Kemampuan menciptakan hal baru"}, {"id": "ha004", "scrambled": "RTLEIASKI", "answer": "LITERASI", "hint": "Kemampuan membaca dan menulis"}, {"id": "ha005", "scrambled": "DNPEIKINA", "answer": "PENDIDIKAN", "hint": "<PERSON><PERSON> be<PERSON> men<PERSON>"}], "tebakKata": [{"id": "tk001", "word": "BAHAGIA", "type": "sinonim", "answers": ["GEMBIRA", "SENANG", "RIANG"], "hint": "Perasaan positif"}, {"id": "tk002", "word": "GELAP", "type": "antonim", "answers": ["TERANG", "CERAH", "BENDERANG"], "hint": "<PERSON><PERSON><PERSON> tanpa cahaya"}, {"id": "tk003", "word": "RAJIN", "type": "sinonim", "answers": ["TEKUN", "GIAT", "ULET"], "hint": "<PERSON><PERSON><PERSON> p<PERSON> keras"}, {"id": "tk004", "word": "BESAR", "type": "antonim", "answers": ["KECIL", "MINI", "MUNGIL"], "hint": "<PERSON><PERSON><PERSON> yang luas"}, {"id": "tk005", "word": "PINTAR", "type": "sinonim", "answers": ["CERDAS", "PANDAI", "GENIUS"], "hint": "<PERSON><PERSON><PERSON><PERSON> be<PERSON> tinggi"}], "merangkaiCerita": [{"id": "mc001", "image": "sunset_beach", "description": "Gambar pantai dengan matahari terbenam", "prompt": "Buatlah cerita pendek berdasarkan gambar pantai saat matahari terbenam"}, {"id": "mc002", "image": "mountain_hiker", "description": "G<PERSON>bar pendaki gunung di puncak", "prompt": "Ceritakan petualangan pendaki yang mencapai puncak gunung"}, {"id": "mc003", "image": "library_books", "description": "<PERSON><PERSON><PERSON><PERSON>an dengan banyak buku", "prompt": "Buatlah cerita tentang seseorang yang menemukan buku ajaib di perpus<PERSON>an"}, {"id": "mc004", "image": "rainy_window", "description": "<PERSON><PERSON><PERSON> hujan di<PERSON> dari jendela", "prompt": "Ceritakan suasana hati seseorang yang melihat hujan dari jendela"}, {"id": "mc005", "image": "garden_flowers", "description": "Gambar taman bunga yang indah", "prompt": "Buatlah cerita tentang anak yang merawat taman bunga"}], "kolaborasiBerdampak": [{"id": "kb001", "title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "description": "Kumpulkan sampah di sekitar area bermain dan pisahkan berdasarkan jenisnya", "duration": 8, "participants": "semua_kelompok"}, {"id": "kb002", "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> anak-anak di sekitar tentang pentingnya membaca dan literasi", "duration": 8, "participants": "semua_kelompok"}, {"id": "kb003", "title": "Kampanye Anti Hoax", "description": "Buat poster atau video singkat tentang bahaya penyebaran hoax", "duration": 8, "participants": "semua_kelompok"}, {"id": "kb004", "title": "<PERSON><PERSON>", "description": "Buat pohon literasi dengan menggantung buku-buku bekas untuk dibagikan gratis", "duration": 8, "participants": "semua_kelompok"}]}