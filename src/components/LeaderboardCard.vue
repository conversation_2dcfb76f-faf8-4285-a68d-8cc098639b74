<template>
  <ion-card class="leaderboard-card">
    <ion-card-header @click="toggleExpanded" class="clickable-header">
      <ion-card-title>
        <div class="leaderboard-header">
          <div class="header-content">
            <ion-icon :icon="trophy" color="warning"></ion-icon>
            Papan Peringkat
          </div>
          <ion-icon
            :icon="isExpanded ? chevronUp : chevronDown"
            class="expand-icon"
          ></ion-icon>
        </div>
      </ion-card-title>
    </ion-card-header>
    <ion-card-content v-show="isExpanded">
      <div class="leaderboard">
        <div
          v-for="(player, index) in leaderboard"
          :key="player.id"
          class="leaderboard-item"
          :class="{ 'current-player': player.id === currentPlayerId }"
        >
          <div class="rank">{{ index + 1 }}</div>
          <ion-avatar class="player-avatar" :class="`avatar-${player.color}`">
            <div class="player-initial">{{ player.name.charAt(0).toUpperCase() }}</div>
          </ion-avatar>
          <div class="player-info">
            <div class="player-name">
              {{ player.name }}
              <ion-icon
                v-if="player.id === currentPlayerId"
                :icon="play"
                color="primary"
                class="current-player-icon"
              ></ion-icon>
            </div>
            <div class="player-position">Posisi: {{ player.position }}</div>
          </div>
          <div class="player-score">{{ player.score }} pts</div>
        </div>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon,
  IonAvatar
} from '@ionic/vue';
import {
  trophy,
  chevronUp,
  chevronDown,
  play
} from 'ionicons/icons';

interface Player {
  id: string;
  name: string;
  position: number;
  score: number;
  color: string;
}

interface Props {
  leaderboard: Player[];
  currentPlayerId: string;
  initialExpanded?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  initialExpanded: true
});

const isExpanded = ref(props.initialExpanded);

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};
</script>

<style scoped>
.leaderboard-card {
  margin-bottom: 16px;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 140, 66, 0.1);
  overflow: hidden;
}

.clickable-header {
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(255, 140, 66, 0.1), rgba(255, 140, 66, 0.05));
}

.clickable-header:hover {
  background: linear-gradient(135deg, rgba(255, 140, 66, 0.2), rgba(255, 140, 66, 0.1));
  transform: translateY(-2px);
  animation: bounce 0.6s ease;
}

.leaderboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-icon {
  transition: transform 0.3s ease;
}

.leaderboard {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 2px solid rgba(74, 144, 226, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.leaderboard-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  animation: bounce 0.6s ease;
}

.leaderboard-item.current-player {
  border: 2px solid var(--literaksi-primary);
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(74, 144, 226, 0.05));
}

.leaderboard-item.current-player::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--bg-gradient-primary);
}

.rank {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
  color: #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  font-size: 14px;
}

.player-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.player-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.player-initial {
  background: var(--ion-color-primary);
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  border-radius: 50%;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.player-initial::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
}

/* Enhanced Avatar color variations with gradients */
.avatar-primary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
}
.avatar-secondary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
}
.avatar-tertiary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-purple), var(--literaksi-purple-dark));
}
.avatar-success .player-initial {
  background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
}
.avatar-warning .player-initial {
  background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
}
.avatar-danger .player-initial {
  background: linear-gradient(135deg, var(--literaksi-danger), var(--literaksi-danger-dark));
}
.avatar-medium .player-initial {
  background: linear-gradient(135deg, #95A5A6, #7F8C8D);
}
.avatar-dark .player-initial {
  background: linear-gradient(135deg, #34495E, #2C3E50);
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.player-name {
  font-weight: 500;
  color: var(--ion-color-dark);
  display: flex;
  align-items: center;
  gap: 6px;
}

.current-player-icon {
  font-size: 0.9rem;
  animation: pulse 1.5s infinite;
}

.player-position {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.player-score {
  font-weight: bold;
  color: var(--ion-color-success);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
</style>
