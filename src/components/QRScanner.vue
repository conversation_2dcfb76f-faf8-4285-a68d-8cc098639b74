<template>
  <div class="qr-scanner-container">
    <ion-header>
      <ion-toolbar>
        <ion-title>Scan Kartu QR</ion-title>
        <ion-buttons slot="end">
          <ion-button @click="closeScanner">
            <ion-icon :icon="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div class="scanner-wrapper">
        <!-- Camera preview area -->
        <div class="camera-preview" ref="cameraPreview">
          <div class="scanning-area">
            <div class="scanner-overlay">
              <div class="scanner-line"></div>
            </div>
            <p class="scanner-instruction">Arahkan kamera ke QR code pada kartu</p>
          </div>
        </div>

        <!-- Manual input fallback -->
        <div class="manual-input-section">
          <ion-button 
            expand="block" 
            fill="outline" 
            @click="toggleManualInput"
            class="manual-input-toggle"
          >
            <ion-icon :icon="keypad" slot="start"></ion-icon>
            Input Manual
          </ion-button>

          <div v-if="showManualInput" class="manual-input">
            <ion-item>
              <ion-label position="stacked">Masukkan ID Kartu</ion-label>
              <ion-input 
                v-model="manualCardId" 
                placeholder="Contoh: hf001, tk002"
                @keyup.enter="processManualInput"
              ></ion-input>
            </ion-item>
            <ion-button 
              expand="block" 
              @click="processManualInput"
              :disabled="!manualCardId"
            >
              Proses Kartu
            </ion-button>
          </div>
        </div>

        <!-- Status messages -->
        <div class="status-section">
          <ion-chip v-if="isScanning" color="primary">
            <ion-icon :icon="scan"></ion-icon>
            <ion-label>Scanning...</ion-label>
          </ion-chip>
          
          <ion-chip v-if="lastError" color="danger">
            <ion-icon :icon="alertCircle"></ion-icon>
            <ion-label>{{ lastError }}</ion-label>
          </ion-chip>
        </div>
      </div>
    </ion-content>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonButtons,
  IonIcon,
  IonItem,
  IonLabel,
  IonInput,
  IonChip
} from '@ionic/vue';
import { 
  close, 
  keypad, 
  scan, 
  alertCircle 
} from 'ionicons/icons';
import { BarcodeScanner } from '@capacitor-mlkit/barcode-scanning';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';

interface QRScannerProps {
  isOpen: boolean;
}

interface QRScannerEmits {
  (e: 'cardScanned', cardId: string): void;
  (e: 'close'): void;
  (e: 'error', error: string): void;
}

const props = defineProps<QRScannerProps>();
const emit = defineEmits<QRScannerEmits>();

const cameraPreview = ref<HTMLElement>();
const isScanning = ref(false);
const showManualInput = ref(false);
const manualCardId = ref('');
const lastError = ref('');

let scanListener: any = null;

onMounted(async () => {
  if (props.isOpen) {
    await initializeScanner();
  }
});

onUnmounted(() => {
  stopScanner();
});

const initializeScanner = async (): Promise<void> => {
  try {
    // Check if barcode scanning is supported
    const isSupported = await BarcodeScanner.isSupported();
    if (!isSupported) {
      throw new Error('Barcode scanning tidak didukung pada perangkat ini');
    }

    // Check if Google Barcode Scanner module is available
    const isGoogleBarcodeScannerModuleAvailable = await BarcodeScanner.isGoogleBarcodeScannerModuleAvailable();
    if (!isGoogleBarcodeScannerModuleAvailable) {
      // Install the module if not available
      await BarcodeScanner.installGoogleBarcodeScannerModule();
    }

    // Request camera permissions
    const granted = await requestCameraPermissions();
    if (!granted) {
      throw new Error('Izin kamera diperlukan untuk memindai QR code');
    }

    await startScanner();
  } catch (error) {
    console.error('Error initializing scanner:', error);
    lastError.value = error instanceof Error ? error.message : 'Gagal menginisialisasi scanner';
    emit('error', lastError.value);
  }
};

const requestCameraPermissions = async (): Promise<boolean> => {
  try {
    const { camera } = await BarcodeScanner.requestPermissions();
    return camera === 'granted' || camera === 'limited';
  } catch (error) {
    console.error('Error requesting camera permissions:', error);
    return false;
  }
};

const startScanner = async (): Promise<void> => {
  try {
    isScanning.value = true;
    lastError.value = '';

    // Add the scanner listener
    scanListener = await BarcodeScanner.addListener('barcodesScanned', (result) => {
      if (result.barcodes && result.barcodes.length > 0) {
        handleScanResult(result.barcodes[0].displayValue);
      }
    });

    // Start scanning
    await BarcodeScanner.startScan();
  } catch (error) {
    console.error('Error starting scanner:', error);
    isScanning.value = false;
    lastError.value = 'Gagal memulai scanning';
    emit('error', lastError.value);
  }
};

const stopScanner = async (): Promise<void> => {
  try {
    isScanning.value = false;
    
    if (scanListener) {
      await scanListener.remove();
      scanListener = null;
    }
    
    await BarcodeScanner.stopScan();
  } catch (error) {
    console.error('Error stopping scanner:', error);
  }
};

const handleScanResult = (scannedValue: string): void => {
  if (!scannedValue) return;

  // Validate and process the scanned card ID
  const cardId = parseCardId(scannedValue);
  if (cardId) {
    emit('cardScanned', cardId);
    closeScanner();
  } else {
    lastError.value = 'QR code tidak valid. Pastikan ini adalah kartu LiterAksi.';
  }
};

const parseCardId = (scannedValue: string): string | null => {
  // Try to extract card ID from scanned value
  // This could be a direct card ID or a JSON object with card info
  try {
    // If it's a JSON string
    if (scannedValue.startsWith('{')) {
      const parsed = JSON.parse(scannedValue);
      return parsed.cardId || parsed.id || null;
    }
    
    // If it's a URL with card ID parameter
    if (scannedValue.includes('cardId=')) {
      const urlParams = new URLSearchParams(scannedValue.split('?')[1]);
      return urlParams.get('cardId');
    }
    
    // If it's just the card ID
    const cardIdPattern = /^(hf|ha|tk|mc|kb)\d{3}$/i;
    if (cardIdPattern.test(scannedValue)) {
      return scannedValue.toLowerCase();
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing scanned value:', error);
    return null;
  }
};

const toggleManualInput = (): void => {
  showManualInput.value = !showManualInput.value;
  if (showManualInput.value) {
    manualCardId.value = '';
  }
};

const processManualInput = (): void => {
  const cardId = manualCardId.value.trim().toLowerCase();
  if (cardId) {
    const validCardId = parseCardId(cardId);
    if (validCardId) {
      emit('cardScanned', validCardId);
      closeScanner();
    } else {
      lastError.value = 'Format ID kartu tidak valid. Gunakan format: hf001, tk002, dll.';
    }
  }
};

const closeScanner = async (): Promise<void> => {
  await stopScanner();
  emit('close');
};

// Alternative scanner using device camera if MLKit fails
const fallbackCameraScanner = async (): Promise<void> => {
  try {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.Uri,
      source: CameraSource.Camera
    });
    
    // Here you would typically use a QR code detection library
    // For now, show manual input as fallback
    showManualInput.value = true;
    lastError.value = 'Gunakan input manual atau coba scan ulang';
  } catch (error) {
    console.error('Camera fallback error:', error);
    showManualInput.value = true;
  }
};
</script>

<style scoped>
.qr-scanner-container {
  height: 100vh;
}

.scanner-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.camera-preview {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scanning-area {
  position: relative;
  width: 250px;
  height: 250px;
  border: 2px solid #fff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--ion-color-primary);
  border-radius: 10px;
}

.scanner-line {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 2px;
  background: var(--ion-color-primary);
  animation: scan-line 2s linear infinite;
}

@keyframes scan-line {
  0% { top: 10%; }
  50% { top: 90%; }
  100% { top: 10%; }
}

.scanner-instruction {
  position: absolute;
  bottom: -50px;
  color: #fff;
  text-align: center;
  font-size: 14px;
}

.manual-input-section {
  padding: 16px;
  background: var(--ion-color-light);
}

.manual-input-toggle {
  margin-bottom: 16px;
}

.manual-input {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.status-section {
  padding: 16px;
  text-align: center;
}

.status-section ion-chip {
  margin: 4px;
}
</style> 