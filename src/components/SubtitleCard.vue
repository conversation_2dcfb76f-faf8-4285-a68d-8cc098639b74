<template>
  <ion-card v-if="isShowingSubtitle && subtitlesEnabled" class="subtitle-card">
    <ion-card-content>
      <div class="subtitle-content">
        <ion-icon :icon="mic" class="subtitle-icon"></ion-icon>
        <p class="subtitle-text">{{ currentSubtitle }}</p>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import {
  IonCard,
  IonCardContent,
  IonIcon
} from '@ionic/vue';
import { mic } from 'ionicons/icons';

interface Props {
  isShowingSubtitle: boolean;
  subtitlesEnabled: boolean;
  currentSubtitle: string;
}

defineProps<Props>();
</script>

<style scoped>
.subtitle-card {
  background: linear-gradient(135deg, var(--ion-color-tertiary-tint), var(--ion-color-tertiary));
  border-left: 4px solid var(--ion-color-tertiary);
  animation: fadeIn 0.3s ease-in-out;
  margin-bottom: 16px;
}

.subtitle-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.subtitle-icon {
  color: var(--ion-color-tertiary-contrast);
  font-size: 1.2rem;
  animation: pulse 1.5s infinite;
}

.subtitle-text {
  margin: 0;
  color: var(--ion-color-tertiary-contrast);
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.4;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>
