<template>
  <ion-modal :is-open="isOpen" @did-dismiss="$emit('close')">
    <ion-page>
      <ion-header>
        <ion-toolbar>
          <ion-title>Tutorial LiterAksi</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="$emit('close')">
              <ion-icon :icon="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content class="tutorial-content">
        <div class="tutorial-sections">
          <!-- Welcome Section -->
          <div class="tutorial-section">
            <div class="tutorial-icon">
              <ion-icon :icon="gameController" color="primary"></ion-icon>
            </div>
            <h2>Selamat Datang di LiterAksi!</h2>
            <p>Aplikasi ini akan membantu Anda bermain board game LiterAksi dengan menggantikan peran instruktur.</p>
          </div>

          <!-- How to Play Section -->
          <div class="tutorial-section">
            <h3><ion-icon :icon="playCircle" color="secondary"></ion-icon> Cara <PERSON></h3>
            <div class="tutorial-steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h4>Sebutkan Angka Dadu</h4>
                  <p>Gunakan suara untuk menyebutkan angka 1-6 (satu, dua, tiga, empat, lima, enam) sebagai pengganti lempar dadu.</p>
                </div>
              </div>
              
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h4>Gerakkan Pemain</h4>
                  <p>Setelah menyebutkan angka, gerakkan pion Anda di papan permainan sesuai angka yang disebutkan.</p>
                </div>
              </div>
              
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h4>Tantangan Otomatis</h4>
                  <p>Jika mendarat di petak kartu, tantangan akan muncul secara otomatis tanpa perlu scan QR code.</p>
                </div>
              </div>
              
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h4>Jawab dengan Suara</h4>
                  <p>Gunakan input suara untuk menjawab tantangan. Aplikasi akan mengevaluasi jawaban Anda.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Card Types Section -->
          <div class="tutorial-section">
            <h3><ion-icon :icon="library" color="tertiary"></ion-icon> Jenis Kartu</h3>
            <div class="card-types">
              <div class="card-type">
                <h4>🔍 Hoax atau Fakta</h4>
                <p>Tentukan apakah informasi yang diberikan adalah hoax atau fakta. Waktu: 2 menit.</p>
              </div>
              
              <div class="card-type">
                <h4>🔤 Huruf Acak</h4>
                <p>Susun huruf-huruf acak menjadi kata yang bermakna. Waktu: 3 menit.</p>
              </div>
              
              <div class="card-type">
                <h4>💭 Tebak Kata</h4>
                <p>Tebak kata berdasarkan sinonim, antonim, atau petunjuk. Waktu: 4 menit.</p>
              </div>
              
              <div class="card-type">
                <h4>📖 Merangkai Cerita</h4>
                <p>Buat cerita berdasarkan gambar atau prompt yang diberikan. Waktu: 6 menit.</p>
              </div>
              
              <div class="card-type">
                <h4>🤝 Kolaborasi Berdampak</h4>
                <p>Kerjakan tantangan bersama tim untuk menciptakan dampak positif. Waktu: 8 menit.</p>
              </div>
            </div>
          </div>

          <!-- Special Tiles Section -->
          <div class="tutorial-section">
            <h3><ion-icon :icon="star" color="warning"></ion-icon> Petak Khusus</h3>
            <div class="special-tiles">
              <div class="tile">
                <h4>🏖️ Rest Area</h4>
                <p>Dapatkan quote motivasi untuk menyemangati permainan.</p>
              </div>
              
              <div class="tile">
                <h4>⬅️ Mundur Wir!!</h4>
                <p>Mundur beberapa langkah secara acak.</p>
              </div>
              
              <div class="tile">
                <h4>⚡ Challenge</h4>
                <p>Dapatkan tantangan khusus dari instruktur.</p>
              </div>
              
              <div class="tile">
                <h4>➕➖ Poin</h4>
                <p>Dapatkan atau kehilangan poin secara otomatis.</p>
              </div>
            </div>
          </div>

          <!-- Tips Section -->
          <div class="tutorial-section">
            <h3><ion-icon :icon="bulb" color="success"></ion-icon> Tips Bermain</h3>
            <ul class="tips-list">
              <li>Pastikan mikrofon berfungsi dengan baik untuk input suara</li>
              <li>Bicara dengan jelas saat menyebutkan angka dadu atau menjawab tantangan</li>
              <li>Gunakan pengaturan subtitle jika ingin melihat teks saat aplikasi berbicara</li>
              <li>Tantangan akan muncul otomatis saat mendarat di petak kartu</li>
              <li>Nikmati permainan dan belajar bersama!</li>
            </ul>
          </div>
        </div>
      </ion-content>

      <ion-footer>
        <ion-toolbar>
          <div class="tutorial-footer">
            <ion-button
              expand="block"
              @click="handleStart"
              color="primary"
              class="start-button"
            >
              <ion-icon :icon="checkmark" slot="start"></ion-icon>
              Mulai Bermain
            </ion-button>
          </div>
        </ion-toolbar>
      </ion-footer>
    </ion-page>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  IonModal,
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonFooter,
  IonButton,
  IonButtons,
  IonIcon,
  IonCheckbox,
  IonLabel
} from '@ionic/vue';
import {
  close,
  gameController,
  playCircle,
  library,
  star,
  bulb,
  checkmark
} from 'ionicons/icons';

interface Props {
  isOpen: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'start', dontShowAgain: boolean): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const dontShowAgain = ref(false);

const handleStart = () => {
  emit('start', dontShowAgain.value);
};
</script>

<style scoped>
.tutorial-content {
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.tutorial-sections {
  padding-bottom: 20px;
}

.tutorial-section {
  margin-bottom: 32px;
}

.tutorial-icon {
  text-align: center;
  margin-bottom: 16px;
}

.tutorial-icon ion-icon {
  font-size: 4rem;
}

.tutorial-section h2 {
  text-align: center;
  color: var(--ion-color-primary);
  margin-bottom: 16px;
}

.tutorial-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--ion-color-dark);
  margin-bottom: 16px;
}

.tutorial-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: var(--ion-color-light);
  border-radius: 12px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: var(--ion-color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: var(--ion-color-primary);
}

.step-content p {
  margin: 0;
  color: var(--ion-color-medium);
  line-height: 1.4;
}

.card-types, .special-tiles {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-type, .tile {
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 255, 0.8));
  border-radius: 15px;
  border-left: 4px solid var(--literaksi-primary);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.card-type:hover, .tile:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.card-type h4, .tile h4 {
  margin: 0 0 8px 0;
  color: var(--literaksi-primary);
  font-weight: bold;
}

.card-type p, .tile p {
  margin: 0;
  color: var(--literaksi-primary-dark);
  font-size: 0.9rem;
  opacity: 0.9;
}

.tips-list {
  padding-left: 20px;
  color: var(--literaksi-primary-dark);
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.tutorial-footer {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(74, 144, 226, 0.05));
  border-top: 2px solid rgba(74, 144, 226, 0.2);
  flex-direction: row-reverse;
}

.dont-show-label {
  flex: 1;
}

.start-button {
  min-width: 140px;
}

@media (max-width: 768px) {
  .tutorial-footer {
    flex-direction: column;
    gap: 16px;
  }
  
  .start-button {
    width: 100%;
  }
}
</style>
