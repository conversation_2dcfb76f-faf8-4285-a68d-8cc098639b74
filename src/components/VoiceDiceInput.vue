<template>
  <div class="dice-section">
    <!-- Input Method Selection -->
    <div class="input-method-selection">
      <ion-segment v-model="selectedInputMethod" @ionChange="handleInputMethodChange">
        <ion-segment-button value="voice">
          <ion-icon :icon="mic"></ion-icon>
          <ion-label>Suara</ion-label>
        </ion-segment-button>
        <ion-segment-button value="manual">
          <ion-icon :icon="keypad"></ion-icon>
          <ion-label>Manual</ion-label>
        </ion-segment-button>
      </ion-segment>
    </div>

    <!-- Voice Input Method -->
    <div v-if="selectedInputMethod === 'voice'" class="voice-input-section">
      <div class="voice-dice-instruction">
        <p><strong>Sebutkan angka 1-6:</strong></p>
        <p class="dice-examples">Contoh: "satu", "dua", "tiga", "empat", "lima", "enam"</p>
      </div>
      <ion-button
        v-if="!isListeningForDice"
        expand="block"
        size="large"
        @click="handleStartVoiceInput"
        :disabled="isProcessing"
        color="tertiary"
      >
        <ion-icon :icon="mic" slot="start"></ion-icon>
        Sebutkan Angka Dadu
      </ion-button>
      <ion-button
        v-else
        expand="block"
        size="large"
        @click="handleStopVoiceInput"
        :disabled="isProcessing"
        color="danger"
      >
        <ion-icon :icon="micOff" slot="start"></ion-icon>
        Hentikan Recording
      </ion-button>
      <div v-if="diceVoiceTranscript" class="dice-voice-result">
        <p><strong>Terdengar:</strong> "{{ diceVoiceTranscript }}"</p>
      </div>
    </div>

    <!-- Manual Input Method -->
    <div v-if="selectedInputMethod === 'manual'" class="manual-input-section">
      <div class="manual-dice-instruction">
        <p><strong>Pilih angka dadu (1-6):</strong></p>
      </div>
      <div class="dice-buttons">
        <ion-button
          v-for="number in [1, 2, 3, 4, 5, 6]"
          :key="number"
          @click="handleManualDiceInput(number)"
          :disabled="isProcessing"
          class="dice-number-button"
          fill="outline"
          color="primary"
        >
          <div class="dice-button-content">
            <div class="dice-number">{{ number }}</div>
            <div class="dice-word">{{ getIndonesianNumber(number) }}</div>
          </div>
        </ion-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonButton,
  IonIcon,
  IonSegment,
  IonSegmentButton,
  IonLabel
} from '@ionic/vue';
import {
  mic,
  micOff,
  keypad
} from 'ionicons/icons';
import { settingsService } from '../services/settingsService';

interface Props {
  isListeningForDice: boolean;
  diceVoiceTranscript: string;
  isProcessing: boolean;
}

interface Emits {
  (e: 'startVoiceInput'): void;
  (e: 'stopVoiceInput'): void;
  (e: 'manualDiceInput', number: number): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

// Local state
const selectedInputMethod = ref<'voice' | 'manual'>('voice');
const selectedDiceNumber = ref<number | null>(null);

// Load saved input method preference on component mount
onMounted(() => {
  selectedInputMethod.value = settingsService.getDiceInputMethod();
});

// Indonesian number mapping
const getIndonesianNumber = (number: number): string => {
  const indonesianNumbers: { [key: number]: string } = {
    1: 'satu',
    2: 'dua',
    3: 'tiga',
    4: 'empat',
    5: 'lima',
    6: 'enam'
  };
  return indonesianNumbers[number] || '';
};

// Event handlers
const handleInputMethodChange = (): void => {
  // Reset selected dice number when switching methods
  selectedDiceNumber.value = null;

  // Save the input method preference
  settingsService.setDiceInputMethod(selectedInputMethod.value);
};

const handleStartVoiceInput = (): void => {
  emit('startVoiceInput');
};

const handleStopVoiceInput = (): void => {
  emit('stopVoiceInput');
};

const handleManualDiceInput = (number: number): void => {
  selectedDiceNumber.value = number;
  // Directly emit the manual dice input without confirmation
  emit('manualDiceInput', number);
  selectedDiceNumber.value = null;
};


</script>

<style scoped>
.dice-section {
  text-align: center;
  margin-bottom: 16px;
}

.input-method-selection {
  margin-bottom: 20px;
}

.voice-input-section,
.manual-input-section {
  margin-top: 16px;
}

.voice-dice-instruction,
.manual-dice-instruction {
  text-align: center;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--ion-color-light);
  border-radius: 8px;
}

.voice-dice-instruction p,
.manual-dice-instruction p {
  margin: 4px 0;
}

.dice-examples {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
  font-style: italic;
}

.dice-voice-result,
.manual-dice-result {
  margin-top: 12px;
  padding: 12px;
  background: var(--ion-color-success-tint);
  border-radius: 8px;
  text-align: center;
}

.dice-voice-result p,
.manual-dice-result p {
  margin: 0 0 12px 0;
  color: var(--ion-color-success-shade);
  font-weight: 500;
}

.manual-dice-result p:last-child {
  margin-bottom: 0;
}

.dice-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin: 20px 0;
  padding: 0 8px;
}

.dice-number-button {
  height: 85px;
  --border-width: 0;
  --border-radius: 20px;
  --background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  --color: white;
  font-weight: bold;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dice-number-button:hover {
  transform: translateY(-3px) scale(1.05);
}

.dice-number-button:active {
  transform: translateY(-1px) scale(1.02);
}

/* Different colors for each dice number */
.dice-number-button:nth-child(1) {
  --background: linear-gradient(135deg, #FF6B6B, #EE5A52);
}

.dice-number-button:nth-child(2) {
  --background: linear-gradient(135deg, #4ECDC4, #44A08D);
}

.dice-number-button:nth-child(3) {
  --background: linear-gradient(135deg, #45B7D1, #3498DB);
}

.dice-number-button:nth-child(4) {
  --background: linear-gradient(135deg, #96CEB4, #27AE60);
}

.dice-number-button:nth-child(5) {
  --background: linear-gradient(135deg, #FFEAA7, #FDCB6E);
  --color: #333;
}

.dice-number-button:nth-child(6) {
  --background: linear-gradient(135deg, #DDA0DD, #9B59B6);
}

.dice-button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.dice-number {
  font-size: 2.2rem;
  font-weight: bold;
  color: inherit;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 4px;
}

.dice-word {
  font-size: 0.9rem;
  color: inherit;
  text-transform: capitalize;
  font-weight: 600;
  opacity: 0.9;
}

/* Responsive design for smaller screens */
@media (max-width: 480px) {
  .dice-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .dice-number-button {
    height: 70px;
  }

  .dice-number {
    font-size: 1.5rem;
  }

  .dice-word {
    font-size: 0.7rem;
  }
}
</style>
