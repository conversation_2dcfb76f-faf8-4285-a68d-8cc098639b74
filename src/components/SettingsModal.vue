<template>
  <ion-modal :is-open="isOpen" @did-dismiss="$emit('close')">
    <ion-page>
      <ion-header>
        <ion-toolbar>
          <ion-title>Pengaturan</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="$emit('close')">
              <ion-icon :icon="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="settings-content">
        <ion-list>
          <!-- Gemini API Configuration -->
          <ion-item-group>
            <ion-item-divider>
              <ion-label>Konfigurasi AI</ion-label>
            </ion-item-divider>
            <ion-item>
              <ion-label position="stacked">Google Gemini API Key</ion-label>
              <ion-input
                :model-value="settingsApiKey"
                @update:model-value="onApiKeyChange"
                type="password"
                placeholder="Masukkan API key"
              ></ion-input>
            </ion-item>
            <ion-item>
              <div class="api-key-buttons">
                <ion-button
                  @click="saveApiKey"
                  :disabled="!settingsApiKey || isTestingApiKey"
                  expand="block"
                  fill="solid"
                  color="primary"
                >
                  <ion-icon :icon="save" slot="start"></ion-icon>
                  Simpan API Key
                </ion-button>
                <ion-button
                  @click="testApiKey"
                  :disabled="!settingsApiKey || isTestingApiKey"
                  expand="block"
                  fill="outline"
                  color="secondary"
                >
                  <ion-icon :icon="isTestingApiKey ? refresh : checkmark" slot="start"></ion-icon>
                  {{ isTestingApiKey ? 'Testing...' : 'Test Koneksi' }}
                </ion-button>
              </div>
            </ion-item>
            <ion-item v-if="geminiInitialized">
              <ion-chip color="success">
                <ion-icon :icon="checkmark"></ion-icon>
                <ion-label>Gemini AI Ready</ion-label>
              </ion-chip>
            </ion-item>
            <ion-item v-else-if="settingsService.hasGeminiApiKey()">
              <ion-chip color="warning">
                <ion-icon :icon="warning"></ion-icon>
                <ion-label>API Key Tersimpan - Perlu Restart</ion-label>
              </ion-chip>
            </ion-item>
            <ion-item v-if="apiKeyValidationStatus">
              <ion-chip :color="apiKeyValidationStatus.color">
                <ion-icon :icon="apiKeyValidationStatus.icon"></ion-icon>
                <ion-label>{{ apiKeyValidationStatus.message }}</ion-label>
              </ion-chip>
            </ion-item>
          </ion-item-group>

          <!-- System Status -->
          <ion-item-divider>
            <ion-label>Status Sistem</ion-label>
          </ion-item-divider>
          <ion-item>
            <ion-label>
              <h3>Voice Recognition (Pengenalan Suara)</h3>
              <p>{{ voiceInputService.isSupported() ? 'Didukung - Bahasa Indonesia' : 'Tidak Didukung' }}</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Text-to-Speech (Suara Indonesia)</h3>
              <p>{{ geminiService.isSpeechAvailable() ? 'Tersedia dengan Suara Indonesia' : 'Tidak Tersedia' }}</p>
              <p v-if="getCurrentVoice()" style="font-size: 0.8em; color: var(--ion-color-medium);">
                Provider: {{ getCurrentVoice()?.name }}
              </p>
              <p v-if="getCurrentTTSProvider()" style="font-size: 0.8em; color: var(--ion-color-success);">
                Menggunakan: {{ selectedTTSProvider }}
              </p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-label>
              <h3>Gemini AI</h3>
              <p>{{ geminiService.isAvailable() ? 'Aktif' : 'Tidak Aktif' }}</p>
            </ion-label>
          </ion-item>
          
          <ion-item>
            <ion-label>
              <h3>Pengaturan Suara Indonesia</h3>
            </ion-label>
          </ion-item>
          
          <ion-item>
            <ion-label position="stacked">Provider Text-to-Speech</ion-label>
            <ion-select 
              v-model="selectedTTSProvider" 
              @ion-change="onTTSProviderChange"
              interface="popover"
              placeholder="Pilih Provider"
              style="margin-top: 10px;"
            >
              <ion-select-option 
                v-for="provider in availableTTSProviders" 
                :key="provider.name"
                :value="provider.name"
                :disabled="!provider.available"
              >
                {{ provider.name }} - {{ provider.description }}
                {{ !provider.available ? ' (Tidak tersedia)' : '' }}
              </ion-select-option>
            </ion-select>
          </ion-item>
          
          <ion-item>
            <div class="checkbox-item">
              <ion-checkbox 
                v-model="autoFallbackEnabled" 
                @ion-change="onAutoFallbackChange"
              >
                <ion-label>
                  <h3>Auto Fallback</h3>
                  <p>Gunakan provider cadangan jika provider utama gagal</p>
                </ion-label>
              </ion-checkbox>
            </div>
          </ion-item>
          <ion-item>
            <div class="api-key-buttons">
              <ion-button
                expand="block"
                fill="outline"
                @click="testIndonesianVoice"
                :disabled="!geminiService.isSpeechAvailable()"
              >
                <ion-icon :icon="mic" slot="start"></ion-icon>
                Test Suara Indonesia
              </ion-button>
              <ion-button
                expand="block"
                fill="clear"
                @click="testCurrentProvider"
                :disabled="!geminiService.isSpeechAvailable()"
                color="secondary"
              >
                <ion-icon :icon="volumeHigh" slot="start"></ion-icon>
                Test Provider Saat Ini
              </ion-button>
              <ion-button 
                expand="block" 
                fill="clear" 
                size="small"
                @click="reloadIndonesianVoice"
                :disabled="!geminiService.isSpeechAvailable()"
              >
                <ion-icon :icon="refresh" slot="start"></ion-icon>
                Muat Ulang Suara Indonesia
              </ion-button>
              <ion-button 
                expand="block" 
                fill="clear" 
                size="small"
                color="secondary"
                @click="testIndonesianTTSService"
              >
                <ion-icon :icon="mic" slot="start"></ion-icon>
                Test TTS Indonesia Langsung
              </ion-button>
            </div>
          </ion-item>

          <!-- Subtitle Settings -->
          <ion-item-divider>
            <ion-label>Pengaturan Subtitle</ion-label>
          </ion-item-divider>
          <ion-item>
            <div class="checkbox-item">
              <ion-checkbox
                v-model="subtitlesEnabled"
                @ion-change="onSubtitlesChange"
              >
                <ion-label>
                  <h3>Tampilkan Subtitle</h3>
                  <p>Tampilkan teks ketika aplikasi sedang berbicara</p>
                </ion-label>
              </ion-checkbox>
            </div>
          </ion-item>

          <!-- Board Display Settings -->
          <ion-item-divider>
            <ion-label>Pengaturan Papan Permainan</ion-label>
          </ion-item-divider>
          <ion-item>
            <div class="checkbox-item">
              <ion-checkbox
                v-model="showBoardEnabled"
                @ion-change="onShowBoardChange"
              >
                <ion-label>
                  <h3>Tampilkan Papan Permainan</h3>
                  <p>Tampilkan visualisasi papan permainan selama gameplay</p>
                </ion-label>
              </ion-checkbox>
            </div>
          </ion-item>
        </ion-list>
      </ion-content>
    </ion-page>
  </ion-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonModal,
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonButtons,
  IonIcon,
  IonList,
  IonItem,
  IonItemGroup,
  IonItemDivider,
  IonLabel,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonCheckbox,
  IonChip,
  toastController
} from '@ionic/vue';
import {
  close,
  save,
  checkmark,
  warning,
  mic,
  refresh,
  closeCircle,
  volumeHigh
} from 'ionicons/icons';
import { useSpeech } from '../composables/useSpeech';
import { geminiService } from '../services/geminiService';
import { voiceInputService } from '../services/voiceInputService';
import { settingsService } from '../services/settingsService';

interface Props {
  isOpen: boolean;
  geminiInitialized: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'update:geminiInitialized', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const settingsApiKey = ref('');
const isTestingApiKey = ref(false);
const apiKeyValidationStatus = ref<{
  color: string;
  icon: string;
  message: string;
} | null>(null);

// Use speech composable
const {
  selectedTTSProvider,
  autoFallbackEnabled,
  availableTTSProviders,
  subtitlesEnabled,
  onTTSProviderChange,
  onAutoFallbackChange,
  onSubtitlesChange,
  testIndonesianTTSService,
  testCurrentProvider,
  reloadIndonesianVoice,
  getCurrentTTSProvider,
  getCurrentVoice
} = useSpeech();

// Board display setting
const showBoardEnabled = ref(true);

const onApiKeyChange = (value: string): void => {
  settingsApiKey.value = value || '';
  // Clear validation status when API key changes
  apiKeyValidationStatus.value = null;
};

const validateApiKey = async (apiKey: string): Promise<boolean> => {
  try {
    // Use Google Generative AI directly to test the API key
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

    // Test with a simple API call
    const result = await model.generateContent('Test');
    const response = result.response;
    response.text(); // This will throw if the API key is invalid

    return true;
  } catch (error) {
    console.error('API key validation failed:', error);
    return false;
  }
};

const saveApiKey = async (): Promise<void> => {
  if (!settingsApiKey.value) {
    return;
  }

  isTestingApiKey.value = true;
  apiKeyValidationStatus.value = null;

  try {
    // First validate the API key
    const isValid = await validateApiKey(settingsApiKey.value);

    if (isValid) {
      settingsService.setGeminiApiKey(settingsApiKey.value);
      geminiService.initialize(settingsApiKey.value);
      emit('update:geminiInitialized', true);

      apiKeyValidationStatus.value = {
        color: 'success',
        icon: checkmark,
        message: 'API Key Valid & Tersimpan'
      };

      const toast = await toastController.create({
        message: 'API Key berhasil disimpan dan divalidasi!',
        duration: 2000,
        color: 'success'
      });
      await toast.present();
    } else {
      apiKeyValidationStatus.value = {
        color: 'danger',
        icon: closeCircle,
        message: 'API Key Tidak Valid'
      };

      const toast = await toastController.create({
        message: 'API Key tidak valid. Periksa kembali API key Anda.',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    }
  } catch (error) {
    console.error('Error saving API key:', error);
    apiKeyValidationStatus.value = {
      color: 'danger',
      icon: warning,
      message: 'Error Validasi API Key'
    };

    const toast = await toastController.create({
      message: 'Terjadi kesalahan saat menyimpan API key.',
      duration: 3000,
      color: 'danger'
    });
    await toast.present();
  } finally {
    isTestingApiKey.value = false;
  }
};

const testApiKey = async (): Promise<void> => {
  if (!settingsApiKey.value) {
    const toast = await toastController.create({
      message: 'Masukkan API key terlebih dahulu',
      duration: 2000,
      color: 'warning'
    });
    await toast.present();
    return;
  }

  isTestingApiKey.value = true;
  apiKeyValidationStatus.value = null;

  try {
    const isValid = await validateApiKey(settingsApiKey.value);

    if (isValid) {
      apiKeyValidationStatus.value = {
        color: 'success',
        icon: checkmark,
        message: 'API Key Valid'
      };

      const toast = await toastController.create({
        message: 'API Key valid! Koneksi Gemini AI berhasil.',
        duration: 2000,
        color: 'success'
      });
      await toast.present();
    } else {
      apiKeyValidationStatus.value = {
        color: 'danger',
        icon: closeCircle,
        message: 'API Key Tidak Valid'
      };

      const toast = await toastController.create({
        message: 'API Key tidak valid. Periksa kembali API key Anda.',
        duration: 3000,
        color: 'danger'
      });
      await toast.present();
    }
  } catch (error) {
    console.error('API test failed:', error);
    apiKeyValidationStatus.value = {
      color: 'danger',
      icon: warning,
      message: 'Error Validasi API Key'
    };

    const toast = await toastController.create({
      message: 'Terjadi kesalahan saat menguji API key.',
      duration: 3000,
      color: 'danger'
    });
    await toast.present();
  } finally {
    isTestingApiKey.value = false;
  }
};

const testIndonesianVoice = async (): Promise<void> => {
  try {
    await geminiService.speak('Halo! Ini adalah tes suara Indonesia untuk aplikasi LiterAksi. Apakah Anda dapat mendengar suara ini dengan jelas?');
    
    const toast = await toastController.create({
      message: '🔊 Tes suara Indonesia berhasil!',
      duration: 3000,
      color: 'success'
    });
    await toast.present();
  } catch (error) {
    console.error('Voice test failed:', error);
    const toast = await toastController.create({
      message: 'Gagal melakukan tes suara',
      duration: 3000,
      color: 'danger'
    });
    await toast.present();
  }
};

// Board display setting handler
const onShowBoardChange = (event: any): void => {
  const enabled = event.detail.checked;
  showBoardEnabled.value = enabled;
  settingsService.setShowBoard(enabled);
};

onMounted(() => {
  // Load API key from settings
  const savedApiKey = settingsService.getGeminiApiKey();
  if (savedApiKey) {
    settingsApiKey.value = savedApiKey;
  }

  // Load board display setting
  showBoardEnabled.value = settingsService.getShowBoard();
});
</script>

<style scoped>
/* Enhanced Settings Modal Styling */
.settings-content {
  --padding-top: 0;
  --padding-bottom: 40px;
  --padding-start: 0;
  --padding-end: 0;
  --background: linear-gradient(180deg, #f8f9ff 0%, #e8f4fd 100%);
}

.settings-content ion-list {
  padding-bottom: 20px;
  background: transparent;
}

/* Enhanced Item Styling */
ion-item {
  --background: rgba(255, 255, 255, 0.8);
  --border-radius: 15px;
  margin: 8px 16px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  --inner-padding-end: 16px;
  --padding-start: 16px;
}

ion-item-divider {
  --background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
  --color: white;
  font-weight: bold;
  margin: 16px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
  width: calc(100% - 32px);
}

ion-item-group {
  margin-bottom: 20px;
}

/* Enhanced Input and Button Styling */
ion-input {
  --color: var(--literaksi-primary);
  font-weight: 500;
}

ion-button {
  --border-radius: 20px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  margin: 4px;
}

ion-button[fill="clear"] {
  --background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
  --color: white;
}

ion-button[color="secondary"] {
  --background: linear-gradient(135deg, var(--literaksi-purple), var(--literaksi-purple-dark));
}

.checkbox-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
  padding: 8px 0;
}

.checkbox-item ion-checkbox {
  margin-top: 2px;
  flex-shrink: 0;
  --background-checked: var(--literaksi-primary);
  --border-color-checked: var(--literaksi-primary);
}

.checkbox-item ion-label {
  flex: 1;
}

.checkbox-item ion-label h3 {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--literaksi-primary);
}

.checkbox-item ion-label p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--literaksi-primary-dark);
  line-height: 1.3;
  opacity: 0.8;
}

/* Enhanced Select Styling */
ion-select {
  --background: rgba(255, 255, 255, 0.9);
  --color: var(--literaksi-primary);
  font-weight: 500;
}

/* Enhanced Chip Styling */
ion-chip {
  --background: linear-gradient(135deg, var(--ion-color-success), var(--ion-color-success-shade));
  --color: white;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

ion-chip[color="warning"] {
  --background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
  --color: #333;
}

/* Enhanced Modal Header */
ion-header ion-toolbar {
  --background: var(--bg-gradient-primary);
  --color: white;
  --border-width: 0;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

ion-header ion-title {
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

ion-header ion-button {
  --color: white;
  --background: rgba(255, 255, 255, 0.2);
  --border-radius: 50%;
}

ion-header ion-button:hover {
  --background: rgba(255, 255, 255, 0.3);
}

/* Enhanced Modal Page */
ion-page {
  --background: linear-gradient(180deg, #f8f9ff 0%, #e8f4fd 100%);
}

/* API Key Buttons Container */
.api-key-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  padding: 8px 0;
}

.api-key-buttons ion-button {
  margin: 0;
  --border-radius: 12px;
  font-weight: 600;
  height: 44px;
}

.api-key-buttons ion-button[fill="solid"] {
  --background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
  --color: white;
  --box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.api-key-buttons ion-button[fill="outline"] {
  --border-color: var(--literaksi-secondary);
  --color: var(--literaksi-secondary);
  --background: rgba(255, 255, 255, 0.8);
}

.api-key-buttons ion-button:disabled {
  opacity: 0.5;
  --box-shadow: none;
}

/* Button Hover Effects */
ion-button:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}
</style>
