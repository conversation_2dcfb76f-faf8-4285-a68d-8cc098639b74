<template>
  <div class="game-finished-section">
    <div class="winner-announcement">
      <ion-icon :icon="trophy" class="winner-icon"></ion-icon>
      <h1>🎉 Selamat! 🎉</h1>
      <h2>{{ winnerName }} Menang!</h2>
      <p><PERSON><PERSON><PERSON><PERSON></p>
    </div>

    <!-- Final Leaderboard -->
    <ion-card class="final-leaderboard">
      <ion-card-header>
        <ion-card-title><PERSON><PERSON></ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div
          v-for="(player, index) in leaderboard"
          :key="player.id"
          class="final-rank-item"
        >
          <div class="rank-badge" :class="`rank-${Math.min(index + 1, 3)}`">
            {{ index + 1 }}
          </div>
          <ion-avatar class="player-avatar" :class="`avatar-${player.color}`">
            <div class="player-initial">{{ player.name.charAt(0).toUpperCase() }}</div>
          </ion-avatar>
          <div class="player-info">
            <div class="player-name">{{ player.name }}</div>
            <div class="player-position">Posisi Akhir: {{ player.position }}</div>
          </div>
          <div class="player-score">{{ player.score }} pts</div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- New Game Button -->
    <ion-button
      expand="block"
      @click="$emit('resetGame')"
      color="primary"
      class="new-game-btn"
    >
      <ion-icon :icon="refresh" slot="start"></ion-icon>
      Permainan Baru
    </ion-button>
  </div>
</template>

<script setup lang="ts">
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon,
  IonAvatar
} from '@ionic/vue';
import { trophy, refresh } from 'ionicons/icons';

interface Player {
  id: string;
  name: string;
  position: number;
  score: number;
  color: string;
}

interface Props {
  winnerName: string;
  leaderboard: Player[];
}

defineProps<Props>();

defineEmits<{
  resetGame: [];
}>();
</script>

<style scoped>
.game-finished-section {
  padding: 32px 16px;
  text-align: center;
}

.winner-announcement {
  max-width: 400px;
  margin: 0 auto;
}

.winner-icon {
  font-size: 4rem;
  color: var(--ion-color-warning);
  margin-bottom: 16px;
}

.final-leaderboard {
  margin: 24px 0;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(74, 144, 226, 0.1);
}

.final-rank-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: var(--ion-color-light);
  border-radius: 8px;
}

.rank-badge {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.rank-1 { 
  background: linear-gradient(135deg, #FFD700, #FFA500);
}
.rank-2 { 
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
}
.rank-3 { 
  background: linear-gradient(135deg, #CD7F32, #B8860B);
}

.player-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.player-initial {
  background: var(--ion-color-primary);
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1rem;
  border-radius: 50%;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.player-initial::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
}

/* Enhanced Avatar color variations with gradients */
.avatar-primary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
}
.avatar-secondary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
}
.avatar-tertiary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-purple), var(--literaksi-purple-dark));
}
.avatar-success .player-initial {
  background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
}
.avatar-warning .player-initial {
  background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
}
.avatar-danger .player-initial {
  background: linear-gradient(135deg, var(--literaksi-danger), var(--literaksi-danger-dark));
}
.avatar-medium .player-initial {
  background: linear-gradient(135deg, #95A5A6, #7F8C8D);
}
.avatar-dark .player-initial {
  background: linear-gradient(135deg, #34495E, #2C3E50);
}

.player-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  text-align: left;
}

.player-name {
  font-weight: 500;
  color: var(--ion-color-dark);
}

.player-position {
  font-size: 0.9rem;
  color: var(--ion-color-medium);
}

.player-score {
  font-weight: bold;
  color: var(--ion-color-success);
}

.new-game-btn {
  margin-top: 24px;
  --border-radius: 25px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  height: 50px;
}

.new-game-btn:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}
</style>
