<template>
  <div v-if="currentCard" class="card-section">
    <GameTimer
      ref="gameTimer"
      :duration="getCardDuration(currentCard.cardType)"
      :auto-start="true"
      @finish="$emit('timerFinish')"
      @warning="$emit('warningSound')"
      @danger="$emit('dangerSound')"
    />

    <ion-card class="challenge-card">
      <ion-card-header>
        <ion-card-title>{{ getCardTypeTitle(currentCard.cardType) }}</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="card-content">
          <p v-if="currentInstruction">{{ currentInstruction }}</p>

          <!-- Input Method Selection -->
          <div class="input-method-selection">
            <ion-segment v-model="answerInputMethod" @ionChange="handleInputMethodChange">
              <ion-segment-button value="voice">
                <ion-icon :icon="mic"></ion-icon>
                <ion-label>Suara</ion-label>
              </ion-segment-button>
              <ion-segment-button value="manual">
                <ion-icon :icon="keypad"></ion-icon>
                <ion-label>Manual</ion-label>
              </ion-segment-button>
            </ion-segment>
          </div>

          <!-- Voice Input Section -->
          <div v-if="answerInputMethod === 'voice'" class="voice-section">
            <ion-button
              v-if="!isListening"
              @click="$emit('startVoiceInput')"
              color="tertiary"
              expand="block"
            >
              <ion-icon :icon="mic" slot="start"></ion-icon>
              Mulai Input Suara
            </ion-button>

            <ion-button
              v-else
              @click="$emit('stopVoiceInput')"
              color="danger"
              expand="block"
            >
              <ion-icon :icon="micOff" slot="start"></ion-icon>
              Hentikan Recording
            </ion-button>

            <div v-if="voiceTranscript" class="voice-transcript">
              <p><strong>Jawaban:</strong> {{ voiceTranscript }}</p>
            </div>
          </div>

          <!-- Manual Input Section -->
          <div v-if="answerInputMethod === 'manual'" class="manual-section">
            <ion-input
              v-model="manualAnswer"
              placeholder="Ketik jawaban Anda di sini..."
              fill="outline"
              class="manual-answer-input"
            ></ion-input>
            <div v-if="manualAnswer" class="manual-answer-preview">
              <p><strong>Jawaban:</strong> {{ manualAnswer }}</p>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <ion-button
              expand="block"
              @click="handleSubmitAnswer"
              :disabled="!canSubmitAnswer"
              color="success"
            >
              <ion-icon :icon="checkmark" slot="start"></ion-icon>
              Submit Jawaban
            </ion-button>
            <ion-button
              expand="block"
              fill="outline"
              @click="handleSkipChallenge"
              color="medium"
            >
              Skip Challenge
            </ion-button>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon,
  IonInput,
  IonSegment,
  IonSegmentButton,
  IonLabel
} from '@ionic/vue';
import {
  mic,
  micOff,
  keypad,
  checkmark
} from 'ionicons/icons';

import GameTimer from './GameTimer.vue';
import { useCardChallenge } from '../composables/useCardChallenge';

interface Props {
  currentCard: any;
  isListening: boolean;
  voiceTranscript: string;
  currentPlayer: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  startVoiceInput: [];
  stopVoiceInput: [];
  timerFinish: [];
  warningSound: [];
  dangerSound: [];
  completeCard: [];
}>();

const {
  answerInputMethod,
  manualAnswer,
  currentInstruction,
  getCardDuration,
  getCardTypeTitle,
  updateCardInstruction,
  processAnswer,
  skipChallenge,
  handleAnswerInputMethodChange,
  clearInputs
} = useCardChallenge();

// Computed property for submit validation
const canSubmitAnswer = computed((): boolean => {
  if (answerInputMethod.value === 'voice') {
    return props.voiceTranscript.trim().length > 0;
  } else {
    return manualAnswer.value.trim().length > 0;
  }
});

// Watch for changes to currentCard and update instructions
watch(() => props.currentCard, (newCard) => {
  if (newCard) {
    // Clear inputs when a new card is set
    clearInputs(() => emit('stopVoiceInput'));
    updateCardInstruction(newCard);
  }
}, { immediate: true });

const handleInputMethodChange = (): void => {
  handleAnswerInputMethodChange(() => emit('stopVoiceInput'));
};

const handleSubmitAnswer = async (): Promise<void> => {
  const answer = answerInputMethod.value === 'voice'
    ? props.voiceTranscript.trim()
    : manualAnswer.value.trim();

  await processAnswer(
    answer,
    props.currentCard,
    props.currentPlayer,
    async () => {
      clearInputs(() => emit('stopVoiceInput'));
      emit('completeCard');
    }
  );
};

const handleSkipChallenge = async (): Promise<void> => {
  await skipChallenge();
  clearInputs(() => emit('stopVoiceInput'));
};
</script>

<style scoped>
.card-section {
  border: 2px solid var(--ion-color-primary);
  border-radius: 12px;
  padding: 16px;
  background: var(--ion-color-primary-tint);
}

.challenge-card {
  margin: 0;
}

.input-method-selection {
  margin: 16px 0;
}

.voice-section {
  margin: 16px 0;
}

.manual-section {
  margin: 16px 0;
}

.manual-answer-input {
  margin-bottom: 12px;
}

.manual-answer-preview {
  margin-top: 12px;
  padding: 12px;
  background: var(--ion-color-light);
  border-radius: 8px;
}

.voice-transcript {
  margin-top: 12px;
  padding: 12px;
  background: var(--ion-color-light);
  border-radius: 8px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

/* Enhanced Button Styles */
.action-buttons ion-button {
  --border-radius: 25px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  font-weight: 600;
  height: 50px;
}

.action-buttons ion-button:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Success Action Button */
.action-buttons ion-button[color="success"] {
  --background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
}

/* Segment button enhancements */
ion-segment-button {
  --background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(74, 144, 226, 0.05));
  --background-checked: var(--bg-gradient-primary);
  --color-checked: white;
  --border-radius: 15px;
  margin: 0 4px;
  font-weight: 600;
}

@media (max-width: 768px) {
  .action-buttons {
    gap: 12px;
  }
}
</style>
