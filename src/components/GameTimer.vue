<template>
  <div class="timer-container" :class="{ 'timer-warning': isWarning, 'timer-danger': isDanger }">
    <div class="timer-display">
      <div class="timer-circle">
        <svg class="timer-svg" :width="size" :height="size">
          <circle
            class="timer-circle-bg"
            :r="radius"
            :cx="center"
            :cy="center"
            fill="transparent"
            :stroke-width="strokeWidth"
          />
          <circle
            class="timer-circle-progress"
            :r="radius"
            :cx="center"
            :cy="center"
            fill="transparent"
            :stroke-width="strokeWidth"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="strokeDashoffset"
            :style="{ stroke: progressColor }"
          />
        </svg>
        <div class="timer-text">
          <div class="timer-time">{{ formattedTime }}</div>
          <div class="timer-label">{{ label }}</div>
        </div>
      </div>
    </div>
    
    <div v-if="showControls" class="timer-controls">
      <ion-button 
        v-if="!isRunning && !isFinished" 
        @click="startTimer" 
        color="success"
        size="small"
      >
        <ion-icon :icon="play" slot="start"></ion-icon>
        Mulai
      </ion-button>
      
             <ion-button 
         v-if="isRunning" 
         @click="pauseTimer" 
         color="warning"
         size="small"
       >
         <ion-icon :icon="pauseIcon" slot="start"></ion-icon>
         Jeda
       </ion-button>
      
      <ion-button 
        v-if="isPaused" 
        @click="resumeTimer" 
        color="primary"
        size="small"
      >
        <ion-icon :icon="play" slot="start"></ion-icon>
        Lanjut
      </ion-button>
      
      <ion-button
        @click="resetTimer"
        color="tertiary"
        size="small"
        fill="outline"
      >
        <ion-icon :icon="refresh" slot="start"></ion-icon>
        Reset
      </ion-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { IonButton, IonIcon } from '@ionic/vue';
import { play, pause as pauseIcon, refresh } from 'ionicons/icons';

interface TimerProps {
  duration: number; // in seconds
  autoStart?: boolean;
  showControls?: boolean;
  label?: string;
  size?: number;
  strokeWidth?: number;
  warningThreshold?: number; // percentage
  dangerThreshold?: number; // percentage
}

interface TimerEmits {
  (e: 'start'): void;
  (e: 'pause'): void;
  (e: 'resume'): void;
  (e: 'finish'): void;
  (e: 'reset'): void;
  (e: 'tick', remaining: number): void;
  (e: 'warning'): void;
  (e: 'danger'): void;
}

const props = withDefaults(defineProps<TimerProps>(), {
  autoStart: false,
  showControls: true,
  label: 'Waktu Tersisa',
  size: 120,
  strokeWidth: 8,
  warningThreshold: 30,
  dangerThreshold: 10
});

const emit = defineEmits<TimerEmits>();

const remainingTime = ref(props.duration);
const isRunning = ref(false);
const isPaused = ref(false);
const isFinished = ref(false);
const startTime = ref(0);
const pausedTime = ref(0);

let intervalId: NodeJS.Timeout | null = null;
let warningEmitted = false;
let dangerEmitted = false;

// Computed properties
const center = computed(() => props.size / 2);
const radius = computed(() => (props.size - props.strokeWidth) / 2);
const circumference = computed(() => 2 * Math.PI * radius.value);

const progress = computed(() => {
  if (props.duration === 0) return 0;
  return remainingTime.value / props.duration;
});

const strokeDashoffset = computed(() => {
  return circumference.value * (1 - progress.value);
});

const progressColor = computed(() => {
  const percentage = (remainingTime.value / props.duration) * 100;
  if (percentage <= props.dangerThreshold) return '#dc2626'; // red-600
  if (percentage <= props.warningThreshold) return '#f59e0b'; // amber-500
  return '#059669'; // emerald-600
});

const isWarning = computed(() => {
  const percentage = (remainingTime.value / props.duration) * 100;
  return percentage <= props.warningThreshold && percentage > props.dangerThreshold;
});

const isDanger = computed(() => {
  const percentage = (remainingTime.value / props.duration) * 100;
  return percentage <= props.dangerThreshold;
});

const formattedTime = computed(() => {
  const minutes = Math.floor(remainingTime.value / 60);
  const seconds = remainingTime.value % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

// Watch for threshold warnings
watch(remainingTime, (newTime) => {
  const percentage = (newTime / props.duration) * 100;
  
  if (percentage <= props.dangerThreshold && !dangerEmitted) {
    dangerEmitted = true;
    emit('danger');
  } else if (percentage <= props.warningThreshold && !warningEmitted) {
    warningEmitted = true;
    emit('warning');
  }
  
  emit('tick', newTime);
});

onMounted(() => {
  if (props.autoStart) {
    startTimer();
  }
});

onUnmounted(() => {
  clearTimer();
});

const startTimer = (): void => {
  if (isFinished.value) {
    resetTimer();
  }
  
  isRunning.value = true;
  isPaused.value = false;
  startTime.value = Date.now();
  
  intervalId = setInterval(() => {
    const elapsed = Math.floor((Date.now() - startTime.value - pausedTime.value) / 1000);
    remainingTime.value = Math.max(0, props.duration - elapsed);
    
    if (remainingTime.value === 0) {
      finishTimer();
    }
  }, 100); // Update every 100ms for smooth animation
  
  emit('start');
};

const pauseTimer = (): void => {
  if (!isRunning.value) return;
  
  clearTimer();
  isRunning.value = false;
  isPaused.value = true;
  
  emit('pause');
};

const resumeTimer = (): void => {
  if (!isPaused.value) return;
  
  const pauseStartTime = Date.now() - startTime.value - pausedTime.value;
  pausedTime.value += Date.now() - (startTime.value + pauseStartTime);
  
  isRunning.value = true;
  isPaused.value = false;
  
  intervalId = setInterval(() => {
    const elapsed = Math.floor((Date.now() - startTime.value - pausedTime.value) / 1000);
    remainingTime.value = Math.max(0, props.duration - elapsed);
    
    if (remainingTime.value === 0) {
      finishTimer();
    }
  }, 100);
  
  emit('resume');
};

const resetTimer = (): void => {
  clearTimer();
  remainingTime.value = props.duration;
  isRunning.value = false;
  isPaused.value = false;
  isFinished.value = false;
  startTime.value = 0;
  pausedTime.value = 0;
  warningEmitted = false;
  dangerEmitted = false;
  
  emit('reset');
};

const finishTimer = (): void => {
  clearTimer();
  remainingTime.value = 0;
  isRunning.value = false;
  isPaused.value = false;
  isFinished.value = true;
  
  emit('finish');
};

const clearTimer = (): void => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};

// Public methods
const start = (): void => startTimer();
const pause = (): void => pauseTimer();
const resume = (): void => resumeTimer();
const reset = (): void => resetTimer();
const stop = (): void => finishTimer();

const getRemainingTime = (): number => remainingTime.value;
const getProgress = (): number => progress.value;
const getIsRunning = (): boolean => isRunning.value;
const getIsFinished = (): boolean => isFinished.value;

// Expose methods to parent
defineExpose({
  start,
  pause,
  resume,
  reset,
  stop,
  getRemainingTime,
  getProgress,
  getIsRunning,
  getIsFinished
});
</script>

<style scoped>
.timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.timer-display {
  position: relative;
}

.timer-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-svg {
  transform: rotate(-90deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.timer-circle-bg {
  stroke: #e5e7eb;
  stroke-linecap: round;
}

.timer-circle-progress {
  stroke-linecap: round;
  transition: stroke-dashoffset 0.1s linear, stroke 0.3s ease;
}

.timer-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.timer-time {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--ion-color-dark);
  line-height: 1;
}

.timer-label {
  font-size: 0.75rem;
  color: var(--ion-color-dark);
  margin-top: 2px;
}

.timer-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

/* Warning and danger states */
.timer-warning {
  animation: pulse-warning 2s infinite;
}

.timer-danger {
  animation: pulse-danger 1s infinite;
}

@keyframes pulse-warning {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes pulse-danger {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.timer-warning .timer-time {
  color: #f59e0b;
}

.timer-danger .timer-time {
  color: #dc2626;
  animation: flash 0.5s infinite;
}

@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive design */
@media (max-width: 480px) {
  .timer-controls {
    flex-direction: column;
    width: 100%;
  }
  
  .timer-controls ion-button {
    flex: 1;
  }
}
</style> 