<template>
  <div class="game-board-container">
    <ion-card class="board-card">
      <ion-card-header>
        <ion-card-title>
          <ion-icon :icon="grid" class="board-icon"></ion-icon>
          Papan Permainan LiterAksi
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="board-grid">
          <div
            v-for="(row, rowIndex) in boardData"
            :key="rowIndex"
            class="board-row"
          >
            <div
              v-for="(square, colIndex) in row"
              :key="colIndex"
              class="board-square"
              :class="getSquareClass(square)"
            >
              <!-- Square content -->
              <div class="square-content">
                <div class="square-type">{{ getSquareDisplayText(square) }}</div>
                
                <!-- Players on this square -->
                <div v-if="getPlayersOnSquare(rowIndex, colIndex).length > 0" class="players-container">
                  <div
                    v-for="player in getPlayersOnSquare(rowIndex, colIndex)"
                    :key="player.id"
                    class="player-token"
                    :style="{ backgroundColor: getPlayerColor(player.color) }"
                  >
                    {{ player.name.charAt(0).toUpperCase() }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Board Legend -->
        <div class="board-legend">
          <h4>Keterangan:</h4>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-square start"></div>
              <span>START</span>
            </div>
            <div class="legend-item">
              <div class="legend-square finish"></div>
              <span>FINISH</span>
            </div>
            <div class="legend-item">
              <div class="legend-square hoax"></div>
              <span>Hoax/Fakta</span>
            </div>
            <div class="legend-item">
              <div class="legend-square huruf-acak"></div>
              <span>Huruf Acak</span>
            </div>
            <div class="legend-item">
              <div class="legend-square tebak-kata"></div>
              <span>Tebak Kata</span>
            </div>
            <div class="legend-item">
              <div class="legend-square merangkai-cerita"></div>
              <span>Merangkai Cerita</span>
            </div>
            <div class="legend-item">
              <div class="legend-square kolaborasi"></div>
              <span>Kolaborasi</span>
            </div>
            <div class="legend-item">
              <div class="legend-square rest-area"></div>
              <span>Rest Area</span>
            </div>
            <div class="legend-item">
              <div class="legend-square challenge"></div>
              <span>Challenge</span>
            </div>
            <div class="legend-item">
              <div class="legend-square mundur"></div>
              <span>Mundur</span>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon
} from '@ionic/vue';
import { grid } from 'ionicons/icons';
import gameConfig from '../data/gameConfig.json';

interface Player {
  id: string;
  name: string;
  position: number;
  score: number;
  color: string;
}

interface Props {
  players: Player[];
}

const props = defineProps<Props>();

// Board data from game config
const boardData = gameConfig.board;

// Convert position to row/col coordinates
const getPositionCoordinates = (position: number): { row: number, col: number } => {
  if (position < 0 || position > 89) {
    return { row: 0, col: 0 };
  }
  
  const row = Math.floor(position / 10);
  const col = position % 10;
  return { row, col };
};

// Create a reactive map of positions to players
const playersByPosition = computed(() => {
  const positionMap: { [key: number]: Player[] } = {};
  props.players.forEach(player => {
    if (!positionMap[player.position]) {
      positionMap[player.position] = [];
    }
    positionMap[player.position].push(player);
  });
  return positionMap;
});

// Get players on a specific square (reactive)
const getPlayersOnSquare = (row: number, col: number) => {
  const position = row * 10 + col;
  return playersByPosition.value[position] || [];
};

// Get square CSS class based on type
const getSquareClass = (squareType: string) => {
  const baseClass = 'square';

  // Map square types to CSS classes
  const typeClassMap: { [key: string]: string } = {
    'START': 'start',
    'FINISH': 'finish',
    'HOAX': 'hoax',
    'HURUF ACAK': 'huruf-acak',
    'TEBAK KATA': 'tebak-kata',
    'MERANGKAI CERITA': 'merangkai-cerita',
    'KOLABORASI BERDAMPAK': 'kolaborasi-berdampak',
    'REST AREA': 'rest-area',
    'CHALLENGE': 'challenge',
    'MUNDUR WIR!!': 'mundur-wir',
    'KOSONG': 'kosong',
    '1+': 'bonus-plus',
    '2+': 'bonus-plus',
    '2-': 'bonus-minus'
  };

  const typeClass = typeClassMap[squareType] || 'kosong';
  return `${baseClass} ${typeClass}`;
};

// Get display text for square
const getSquareDisplayText = (squareType: string): string => {
  const displayMap: { [key: string]: string } = {
    'START': 'START',
    'FINISH': 'FINISH',
    'HOAX': 'H/F',
    'HURUF ACAK': 'HA',
    'TEBAK KATA': 'TK',
    'MERANGKAI CERITA': 'MC',
    'KOLABORASI BERDAMPAK': 'KB',
    'REST AREA': 'REST',
    'CHALLENGE': 'CHAL',
    'MUNDUR WIR!!': 'BACK',
    'KOSONG': '',
    '1+': '+1',
    '2+': '+2',
    '2-': '-2'
  };
  
  return displayMap[squareType] || squareType;
};

// Get player color
const getPlayerColor = (colorName: string): string => {
  const colorMap: { [key: string]: string } = {
    'primary': '#3880ff',
    'secondary': '#3dc2ff',
    'tertiary': '#5260ff',
    'success': '#2dd36f',
    'warning': '#ffc409',
    'danger': '#eb445a',
    'medium': '#92949c',
    'dark': '#222428'
  };
  
  return colorMap[colorName] || '#3880ff';
};
</script>

<style scoped>
.game-board-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.board-card {
  margin: 16px;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(74, 144, 226, 0.1);
  overflow: hidden;
}

.board-icon {
  margin-right: 8px;
  color: var(--literaksi-primary);
}

.board-grid {
  display: flex;
  flex-direction: column;
  gap: 3px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 12px;
  border-radius: 15px;
  overflow-x: auto;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.board-row {
  display: flex;
  gap: 2px;
}

.board-square {
  min-width: 60px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  position: relative;
  font-size: 10px;
  font-weight: bold;
  text-align: center;
  border: 1px solid #ddd;
}

.square-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.square-type {
  font-size: 9px;
  font-weight: bold;
  text-align: center;
  line-height: 1;
}

.players-container {
  position: absolute;
  bottom: 2px;
  left: 2px;
  right: 2px;
  display: flex;
  flex-wrap: wrap;
  gap: 1px;
  justify-content: center;
}

.player-token {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 2px rgba(0,0,0,0.5);
  border: 1px solid rgba(255,255,255,0.3);
}

/* Square type styles - Enhanced with vibrant colors and effects */
.square.start {
  background: linear-gradient(135deg, #2ECC71, #27AE60);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.start::before {
  content: '🚀';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.finish {
  background: linear-gradient(135deg, #F1C40F, #D4AC0D);
  color: #333;
  box-shadow: 0 4px 15px rgba(241, 196, 15, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
}

.square.finish::before {
  content: '🏆';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.hoax {
  background: linear-gradient(135deg, var(--card-hoax-start), var(--card-hoax-end));
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.hoax::before {
  content: '🔍';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.huruf-acak {
  background: linear-gradient(135deg, var(--card-huruf-start), var(--card-huruf-end));
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.huruf-acak::before {
  content: '🔤';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.tebak-kata {
  background: linear-gradient(135deg, var(--card-tebak-start), var(--card-tebak-end));
  color: white;
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.tebak-kata::before {
  content: '💭';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.merangkai-cerita {
  background: linear-gradient(135deg, var(--card-cerita-start), var(--card-cerita-end));
  color: white;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.merangkai-cerita::before {
  content: '📖';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.kolaborasi-berdampak {
  background: linear-gradient(135deg, var(--card-kolaborasi-start), var(--card-kolaborasi-end));
  color: white;
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.kolaborasi-berdampak::before {
  content: '🤝';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.rest-area {
  background: linear-gradient(135deg, #48CAE4, #0077B6);
  color: white;
  box-shadow: 0 4px 15px rgba(72, 202, 228, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.rest-area::before {
  content: '🏖️';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.challenge {
  background: linear-gradient(135deg, #FF6B6B, #EE5A52);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.challenge::before {
  content: '⚡';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.mundur-wir {
  background: linear-gradient(135deg, #FF8E53, #FF6B35);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 142, 83, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.mundur-wir::before {
  content: '⬅️';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
  color: white;
}

.square.kosong {
  background: linear-gradient(135deg, #F8F9FF, #E8F4FD);
  color: #6c757d;
  border: 2px dashed rgba(108, 117, 125, 0.3);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.square.bonus-plus {
  background: linear-gradient(135deg, #2ECC71, #27AE60);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.bonus-plus::before {
  content: '➕';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

.square.bonus-minus {
  background: linear-gradient(135deg, #E74C3C, #C0392B);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.square.bonus-minus::before {
  content: '➖';
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
}

/* Add hover effects for all squares */
.square:hover {
  transform: scale(1.05);
  transition: all 0.3s ease;
  z-index: 2;
}

/* Add animation for active squares */
.square.active {
  animation: pulse-colorful 1.5s infinite;
}

/* Board Legend - Enhanced with colorful design */
.board-legend {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(74, 144, 226, 0.1);
}

.board-legend h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: var(--literaksi-primary);
  font-weight: bold;
  text-align: center;
}

.legend-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.legend-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.legend-square {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 1px solid #ddd;
}

.legend-square.start { background: linear-gradient(135deg, #2dd36f, #10dc60); }
.legend-square.finish { background: linear-gradient(135deg, #ffc409, #ffcd29); }
.legend-square.hoax { background: linear-gradient(135deg, #eb445a, #f04141); }
.legend-square.huruf-acak { background: linear-gradient(135deg, #3880ff, #4c8dff); }
.legend-square.tebak-kata { background: linear-gradient(135deg, #5260ff, #6370ff); }
.legend-square.merangkai-cerita { background: linear-gradient(135deg, #3dc2ff, #50c8ff); }
.legend-square.kolaborasi { background: linear-gradient(135deg, #92949c, #a2a4ab); }
.legend-square.rest-area { background: linear-gradient(135deg, #2dd36f, #42d77d); }
.legend-square.challenge { background: linear-gradient(135deg, #ffc409, #ffce31); }
.legend-square.mundur { background: linear-gradient(135deg, #eb445a, #f25454); }

/* Responsive design */
@media (max-width: 768px) {
  .board-square {
    min-width: 45px;
    min-height: 45px;
    font-size: 8px;
  }
  
  .square-type {
    font-size: 7px;
  }
  
  .player-token {
    width: 12px;
    height: 12px;
    font-size: 6px;
  }
  
  .legend-items {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
  
  .legend-item {
    font-size: 10px;
  }
}
</style>
