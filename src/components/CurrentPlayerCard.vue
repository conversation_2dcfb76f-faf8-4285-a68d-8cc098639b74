<template>
  <ion-card class="current-player-card">
    <ion-card-content>
      <div class="current-player-info">
        <ion-avatar class="player-avatar" :class="`avatar-${currentPlayer.color}`">
          <div class="player-initial">{{ currentPlayer.name.charAt(0).toUpperCase() }}</div>
        </ion-avatar>
        <div class="player-details">
          <h3>{{ currentPlayer.name }}</h3>
          <p><PERSON><PERSON><PERSON> ke-{{ currentTurn }}</p>
          <div class="player-stats">
            <ion-chip color="primary">
              <ion-icon :icon="location"></ion-icon>
              <ion-label>Posisi: {{ currentPlayer.position }}</ion-label>
            </ion-chip>
            <ion-chip color="success">
              <ion-icon :icon="trophy"></ion-icon>
              <ion-label>Skor: {{ currentPlayer.score }}</ion-label>
            </ion-chip>
          </div>
        </div>
      </div>
    </ion-card-content>
  </ion-card>
</template>

<script setup lang="ts">
import {
  IonCard,
  IonCardContent,
  IonAvatar,
  IonChip,
  IonIcon,
  IonLabel
} from '@ionic/vue';
import { location, trophy } from 'ionicons/icons';

interface Player {
  id: string;
  name: string;
  position: number;
  score: number;
  color: string;
}

interface Props {
  currentPlayer: Player;
  currentTurn: number;
}

defineProps<Props>();
</script>

<style scoped>
.current-player-card {
  margin-bottom: 16px;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.15);
  border: 2px solid rgba(74, 144, 226, 0.1);
  overflow: hidden;
  position: relative;
}

.current-player-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--bg-gradient-primary);
}

.current-player-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.player-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: pulse-colorful 2s infinite;
  border: 3px solid var(--literaksi-warning);
}

.player-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.player-initial {
  background: var(--ion-color-primary);
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  border-radius: 50%;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.player-initial::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
  border-radius: 50%;
}

/* Enhanced Avatar color variations with gradients */
.avatar-primary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
}
.avatar-secondary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
}
.avatar-tertiary .player-initial {
  background: linear-gradient(135deg, var(--literaksi-purple), var(--literaksi-purple-dark));
}
.avatar-success .player-initial {
  background: linear-gradient(135deg, var(--literaksi-success), var(--literaksi-success-dark));
}
.avatar-warning .player-initial {
  background: linear-gradient(135deg, var(--literaksi-warning), var(--literaksi-warning-dark));
}
.avatar-danger .player-initial {
  background: linear-gradient(135deg, var(--literaksi-danger), var(--literaksi-danger-dark));
}
.avatar-medium .player-initial {
  background: linear-gradient(135deg, #95A5A6, #7F8C8D);
}
.avatar-dark .player-initial {
  background: linear-gradient(135deg, #34495E, #2C3E50);
}

.player-details h3 {
  margin: 0 0 4px 0;
  color: var(--ion-color-primary);
}

.player-details p {
  margin: 0 0 8px 0;
  color: var(--ion-color-medium);
}

.player-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .current-player-info {
    flex-direction: column;
    text-align: center;
  }

  .player-stats {
    justify-content: center;
  }
}

@keyframes pulse-colorful {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>
