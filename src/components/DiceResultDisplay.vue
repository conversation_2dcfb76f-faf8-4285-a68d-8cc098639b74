<template>
  <div v-if="diceRoll" class="dice-result">
    <div class="dice-display">
      <div class="dice-value">{{ diceRoll }}</div>
      <p>Maju {{ diceRoll }} langkah</p>
      <p v-if="isProcessing" class="processing-message">
        <ion-spinner name="dots"></ion-spinner>
        Menggerakkan pemain...
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IonSpinner } from '@ionic/vue';

interface Props {
  diceRoll: number | null;
  isProcessing: boolean;
}

defineProps<Props>();
</script>

<style scoped>
.dice-result {
  text-align: center;
  background: var(--bg-gradient-primary);
  color: white;
  padding: 20px;
  border-radius: 20px;
  margin: 16px 0;
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.dice-display {
  margin-bottom: 16px;
}

.dice-value {
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.processing-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 12px;
  font-style: italic;
}

.dice-result p {
  margin: 8px 0;
  font-weight: 500;
}
</style>
