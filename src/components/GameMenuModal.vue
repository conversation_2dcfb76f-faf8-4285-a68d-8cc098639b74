<template>
  <ion-modal :is-open="isOpen" @did-dismiss="$emit('close')">
    <ion-page>
      <ion-header>
        <ion-toolbar>
          <ion-title>Menu Permainan</ion-title>
          <ion-buttons slot="end">
            <ion-button @click="$emit('close')">
              <ion-icon :icon="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <ion-list>
          <ion-item button @click="handleExitGame">
            <ion-icon :icon="exitOutline" slot="start" color="danger"></ion-icon>
            <ion-label>
              <h2>Keluar dari Permainan</h2>
              <p>Keluar dan kembali ke menu utama</p>
            </ion-label>
          </ion-item>
          <ion-item button @click="handleResetGame">
            <ion-icon :icon="refresh" slot="start" color="warning"></ion-icon>
            <ion-label>
              <h2>Reset Permainan</h2>
              <p><PERSON><PERSON> permainan baru dengan pemain yang sama</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-content>
    </ion-page>
  </ion-modal>
</template>

<script setup lang="ts">
import {
  IonModal,
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonButton,
  IonContent,
  IonList,
  IonItem,
  IonIcon,
  IonLabel
} from '@ionic/vue';
import {
  close,
  exitOutline,
  refresh
} from 'ionicons/icons';

interface Props {
  isOpen: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  close: [];
  exitGame: [];
  resetGame: [];
}>();

const handleExitGame = () => {
  emit('close');
  emit('exitGame');
};

const handleResetGame = () => {
  emit('close');
  emit('resetGame');
};
</script>

<style scoped>
ion-modal {
  --background: linear-gradient(135deg, #ffffff, #f8f9ff);
}

ion-header ion-toolbar {
  --background: var(--bg-gradient-primary);
  --color: white;
  --border-width: 0;
  box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

ion-header ion-title {
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

ion-list {
  background: transparent;
  padding: 16px;
}

ion-item {
  --background: linear-gradient(135deg, #ffffff, #f8f9ff);
  --border-radius: 15px;
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 2px solid rgba(74, 144, 226, 0.1);
  transition: all 0.3s ease;
}

ion-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

ion-label h2 {
  font-weight: 600;
  color: var(--ion-color-dark);
  margin-bottom: 4px;
}

ion-label p {
  color: var(--ion-color-medium);
  font-size: 0.9rem;
}
</style>
