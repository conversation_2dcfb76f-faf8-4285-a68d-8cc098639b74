<template>
  <div class="game-setup-section">
    <!-- API Key Warning -->
    <ion-card v-if="!geminiInitialized" class="api-warning">
      <ion-card-content>
        <div class="warning-content">
          <ion-icon :icon="warning" color="warning"></ion-icon>
          <div>
            <h3><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h3>
            <p>Silakan masukkan Google Gemini API Key di pengaturan untuk menggunakan fitur AI.</p>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Player Setup -->
    <ion-card class="setup-card">
      <ion-card-header>
        <ion-card-title>Setup Pemain</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-item>
          <ion-label position="stacked">Nama Pemain</ion-label>
          <ion-input
            :model-value="newPlayerName"
            @update:model-value="(value: string) => emit('update:newPlayerName', value)"
            placeholder="Masukkan nama pemain"
            @keyup.enter="addPlayer"
          ></ion-input>
        </ion-item>
        <ion-button
          expand="block"
          @click="addPlayer"
          :disabled="!newPlayerName || !newPlayerName.trim()"
        >
          <ion-icon :icon="add" slot="start"></ion-icon>
          Tambah Pemain
        </ion-button>

        <!-- Player List -->
        <div v-if="playerNames.length > 0" class="player-list">
          <h4>Pemain ({{ playerNames.length }}/8):</h4>
          <div class="players">
            <ion-chip
              v-for="(name, index) in playerNames"
              :key="index"
              :color="getPlayerColor(index)"
              @click="removePlayer(index)"
            >
              <ion-avatar>
                <div class="player-initial">{{ name.charAt(0).toUpperCase() }}</div>
              </ion-avatar>
              <ion-label>{{ name }}</ion-label>
              <ion-icon :icon="closeCircle"></ion-icon>
            </ion-chip>
          </div>
        </div>

        <!-- Start Game Button -->
        <ion-button
          v-if="playerNames.length >= 2"
          expand="block"
          color="primary"
          size="large"
          @click="startGame"
          class="start-game-btn"
        >
          <ion-icon :icon="play" slot="start"></ion-icon>
          Mulai Permainan
        </ion-button>
        <p v-else class="min-players-note">
          Minimal 2 pemain untuk memulai permainan
        </p>
      </ion-card-content>
    </ion-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonChip,
  IonAvatar,
  toastController
} from '@ionic/vue';
import {
  warning,
  save,
  checkmark,
  add,
  play,
  closeCircle
} from 'ionicons/icons';
import { geminiService } from '../services/geminiService';
import { settingsService } from '../services/settingsService';

interface Props {
  playerNames: string[];
  newPlayerName: string;
  geminiInitialized: boolean;
}

interface Emits {
  (e: 'update:playerNames', value: string[]): void;
  (e: 'update:newPlayerName', value: string): void;
  (e: 'update:geminiInitialized', value: boolean): void;
  (e: 'startGame'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const settingsApiKey = ref('');

// Player colors
const playerColors = ['primary', 'secondary', 'tertiary', 'success', 'warning', 'danger', 'medium', 'dark'];

const getPlayerColor = (index: number): string => {
  return playerColors[index % playerColors.length];
};

const updateApiKey = (event: CustomEvent): void => {
  settingsApiKey.value = event.detail.value;
};

const addPlayer = (): void => {
  const trimmedName = props.newPlayerName?.trim() || '';
  if (trimmedName && props.playerNames.length < 8) {
    const newPlayers = [...props.playerNames, trimmedName];
    emit('update:playerNames', newPlayers);
    emit('update:newPlayerName', '');
  }
};

const removePlayer = (index: number): void => {
  const newPlayers = [...props.playerNames];
  newPlayers.splice(index, 1);
  emit('update:playerNames', newPlayers);
};

const saveApiKey = async (): Promise<void> => {
  if (settingsApiKey.value && settingsApiKey.value.length > 10) {
    settingsService.setGeminiApiKey(settingsApiKey.value);
    geminiService.initialize(settingsApiKey.value);
    emit('update:geminiInitialized', true);
    
    const toast = await toastController.create({
      message: 'API Key berhasil disimpan!',
      duration: 2000,
      color: 'success'
    });
    await toast.present();
  }
};

const testApiKey = async (): Promise<void> => {
  if (!props.geminiInitialized) {
    const toast = await toastController.create({
      message: 'Silakan simpan API key terlebih dahulu',
      duration: 2000,
      color: 'warning'
    });
    await toast.present();
    return;
  }

  try {
    // Test the API by generating a simple instruction
    await geminiService.generateGameInstructions('TEST', { type: 'TEST' });

    const toast = await toastController.create({
      message: 'Koneksi Gemini AI berhasil!',
      duration: 2000,
      color: 'success'
    });
    await toast.present();
  } catch (error) {
    console.error('API test failed:', error);
    const toast = await toastController.create({
      message: 'Koneksi Gemini AI gagal. Periksa API key.',
      duration: 3000,
      color: 'danger'
    });
    await toast.present();
  }
};

const startGame = (): void => {
  emit('startGame');
};

onMounted(() => {
  // Load API key from settings
  const savedApiKey = settingsService.getGeminiApiKey();
  if (savedApiKey) {
    settingsApiKey.value = savedApiKey;
    geminiService.initialize(savedApiKey);
    emit('update:geminiInitialized', true);
  }
});
</script>

<style scoped>
.game-setup-section {
  padding: 16px;
  background: linear-gradient(180deg, #f8f9ff 0%, #e8f4fd 100%);
  min-height: 100vh;
}

.api-warning {
  background: linear-gradient(135deg, #FFF3CD, #FCF4A3);
  border: 2px solid var(--literaksi-warning);
  border-radius: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 25px rgba(241, 196, 15, 0.2);
  overflow: hidden;
  position: relative;
}

.api-warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--literaksi-warning), var(--literaksi-warning-dark));
}

.warning-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0;
}

.warning-content ion-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
  color: var(--literaksi-warning-dark);
}

.warning-content h3 {
  margin: 0 0 8px 0;
  color: var(--literaksi-warning-dark);
  font-weight: bold;
  font-size: 1.2rem;
}

.warning-content p {
  margin: 0;
  color: #6B5B00;
  font-weight: 500;
}

.setup-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff, #f8f9ff);
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(74, 144, 226, 0.1);
  overflow: hidden;
}

.api-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.api-status {
  margin-top: 12px;
}

.player-list {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(74, 144, 226, 0.05));
  border-radius: 15px;
  border: 2px solid rgba(74, 144, 226, 0.2);
}

.player-list h4 {
  margin: 0 0 16px 0;
  color: var(--literaksi-primary);
  font-weight: bold;
  text-align: center;
  font-size: 1.1rem;
}

.players {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.player-initial {
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.start-game-btn {
  margin-top: 24px;
  --border-radius: 25px;
  --box-shadow: 0 6px 20px rgba(74, 144, 226, 0.3);
  --background: linear-gradient(135deg, var(--literaksi-primary), var(--literaksi-primary-dark));
  height: 56px;
  font-weight: bold;
  font-size: 1.1rem;
}

.start-game-btn:hover {
  transform: translateY(-2px);
  --box-shadow: 0 8px 25px rgba(74, 144, 226, 0.4);
}

.min-players-note {
  text-align: center;
  color: var(--literaksi-primary);
  font-style: italic;
  margin-top: 20px;
  padding: 12px;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 10px;
  font-weight: 500;
}

/* Enhanced Button Styles */
ion-button {
  --border-radius: 20px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  transition: all 0.3s ease;
}

ion-button:not(.start-game-btn) {
  --background: linear-gradient(135deg, var(--literaksi-secondary), var(--literaksi-secondary-dark));
}

ion-button:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Enhanced Input Styles */
ion-item {
  --background: rgba(255, 255, 255, 0.8);
  --border-radius: 15px;
  margin-bottom: 16px;
  border-radius: 15px;
}

ion-input {
  --color: var(--literaksi-primary);
  font-weight: 500;
}

ion-label {
  color: var(--literaksi-primary) !important;
  font-weight: 600;
}

/* Enhanced Chip Styles */
ion-chip {
  --background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  --color: white;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

ion-chip:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .api-actions {
    flex-direction: column;
  }

  .api-actions ion-button {
    width: 100%;
  }

  .game-setup-section {
    padding: 12px;
  }
}
</style>
